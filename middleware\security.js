const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const helmet = require('helmet');
const hpp = require('hpp');
const xss = require('xss-clean');
const csrf = require('csurf');
const securityConfig = require('../config/security-config');
const authService = require('../services/auth-service');
const permissionService = require('../services/permission-service');
const auditLogger = require('../services/audit-logger');

/**
 * Enhanced Security Middleware
 * Comprehensive security protection for the application
 */

/**
 * Rate limiting middleware
 */
const createRateLimiter = (options = {}) => {
    const config = securityConfig.get('rateLimiting');
    
    return rateLimit({
        windowMs: options.windowMs || config.windowMs,
        max: options.max || config.maxRequests,
        message: options.message || 'Too many requests from this IP, please try again later.',
        standardHeaders: true,
        legacyHeaders: false,
        handler: async (req, res, next) => {
            await auditLogger.logSecurityEvent(
                'rate_limit_exceeded',
                req.user?.user_id || null,
                req.ip,
                {
                    endpoint: req.path,
                    method: req.method,
                    userAgent: req.get('User-Agent')
                },
                'medium'
            );
            
            res.status(429).json({
                error: 'Rate limit exceeded',
                message: options.message || 'Too many requests from this IP, please try again later.',
                retryAfter: Math.round(options.windowMs / 1000) || Math.round(config.windowMs / 1000)
            });
        },
        skip: (req) => {
            // Skip rate limiting for whitelisted IPs
            return securityConfig.isIPWhitelisted(req.ip);
        }
    });
};

/**
 * Slow down middleware for progressive delays
 */
const createSlowDown = (options = {}) => {
    const config = securityConfig.get('rateLimiting');
    
    return slowDown({
        windowMs: options.windowMs || config.windowMs,
        delayAfter: options.delayAfter || Math.floor(config.maxRequests * 0.5),
        delayMs: options.delayMs || 500,
        maxDelayMs: options.maxDelayMs || 20000,
        skip: (req) => {
            return securityConfig.isIPWhitelisted(req.ip);
        }
    });
};

/**
 * Brute force protection for login endpoints
 */
const bruteForceProtection = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts per window
    skipSuccessfulRequests: true,
    handler: async (req, res, next) => {
        await auditLogger.logSecurityEvent(
            'brute_force_attempt',
            null,
            req.ip,
            {
                endpoint: req.path,
                username: req.body.username,
                userAgent: req.get('User-Agent')
            },
            'high'
        );
        
        res.status(429).json({
            error: 'Too many login attempts',
            message: 'Account temporarily locked due to too many failed login attempts',
            retryAfter: 900 // 15 minutes
        });
    }
});

/**
 * Security headers middleware
 */
const securityHeaders = helmet({
    contentSecurityPolicy: {
        directives: securityConfig.get('csp').directives,
        reportOnly: false
    },
    hsts: {
        maxAge: ********,
        includeSubDomains: true,
        preload: true
    },
    noSniff: true,
    xssFilter: true,
    referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
});

/**
 * XSS protection middleware
 */
const xssProtection = xss();

/**
 * HTTP Parameter Pollution protection
 */
const hppProtection = hpp({
    whitelist: ['tags', 'categories'] // Allow arrays for these parameters
});

/**
 * CSRF protection middleware
 */
const csrfProtection = csrf({
    cookie: {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict'
    }
});

/**
 * Enhanced authentication middleware
 */
const requireAuth = async (req, res, next) => {
    try {
        const sessionId = req.session?.sessionId || req.headers['x-session-id'];
        
        if (!sessionId) {
            return res.status(401).json({ error: 'Authentication required' });
        }

        // Validate session
        const session = await validateSession(sessionId, req.ip, req.get('User-Agent'));
        if (!session) {
            return res.status(401).json({ error: 'Invalid or expired session' });
        }

        // Get user details
        const user = await authService.findUserById(session.user_id);
        if (!user || user.account_status !== 'active') {
            return res.status(401).json({ error: 'Account not active' });
        }

        // Check session security
        if (session.ip_address !== req.ip) {
            await auditLogger.logSecurityEvent(
                'session_ip_mismatch',
                user.user_id,
                req.ip,
                {
                    sessionIp: session.ip_address,
                    requestIp: req.ip,
                    sessionId: sessionId
                },
                'high'
            );
            
            return res.status(401).json({ error: 'Session security violation' });
        }

        // Update session activity
        await updateSessionActivity(sessionId);

        // Attach user to request
        req.user = user;
        req.session = session;

        // Log data access if configured
        if (securityConfig.get('audit').logDataAccess) {
            await auditLogger.logDataAccess(
                user.user_id,
                'api_endpoint',
                req.path,
                req.method.toLowerCase(),
                'general',
                `API access: ${req.method} ${req.path}`,
                req.ip,
                sessionId
            );
        }

        next();
    } catch (error) {
        console.error('Authentication middleware error:', error);
        res.status(500).json({ error: 'Authentication failed' });
    }
};

/**
 * Permission-based authorization middleware
 */
const requirePermission = (permissionName, resourceIdParam = null) => {
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({ error: 'Authentication required' });
            }

            const resourceId = resourceIdParam ? req.params[resourceIdParam] : null;
            const hasPermission = await permissionService.hasPermission(
                req.user.user_id,
                permissionName,
                resourceId
            );

            if (!hasPermission) {
                await auditLogger.logSecurityEvent(
                    'unauthorized_access_attempt',
                    req.user.user_id,
                    req.ip,
                    {
                        permission: permissionName,
                        resource: resourceId,
                        endpoint: req.path,
                        method: req.method
                    },
                    'medium'
                );

                return res.status(403).json({ 
                    error: 'Insufficient permissions',
                    required: permissionName 
                });
            }

            // Log authorized access
            await auditLogger.log(
                req.user.user_id,
                'authorized_access',
                'permission',
                permissionName,
                null,
                { endpoint: req.path, method: req.method },
                req.ip,
                null,
                req.session?.session_id,
                'info',
                'authorization'
            );

            next();
        } catch (error) {
            console.error('Authorization middleware error:', error);
            res.status(500).json({ error: 'Authorization failed' });
        }
    };
};

/**
 * Role-based authorization middleware
 */
const requireRole = (roles) => {
    const roleArray = Array.isArray(roles) ? roles : [roles];
    
    return async (req, res, next) => {
        try {
            if (!req.user) {
                return res.status(401).json({ error: 'Authentication required' });
            }

            if (!roleArray.includes(req.user.role)) {
                await auditLogger.logSecurityEvent(
                    'unauthorized_role_access',
                    req.user.user_id,
                    req.ip,
                    {
                        userRole: req.user.role,
                        requiredRoles: roleArray,
                        endpoint: req.path,
                        method: req.method
                    },
                    'medium'
                );

                return res.status(403).json({ 
                    error: 'Insufficient role permissions',
                    required: roleArray 
                });
            }

            next();
        } catch (error) {
            console.error('Role authorization middleware error:', error);
            res.status(500).json({ error: 'Authorization failed' });
        }
    };
};

/**
 * Input validation and sanitization middleware
 */
const validateInput = (schema) => {
    return (req, res, next) => {
        try {
            // Basic input validation and sanitization
            if (req.body) {
                req.body = sanitizeObject(req.body);
            }
            
            if (req.query) {
                req.query = sanitizeObject(req.query);
            }

            if (req.params) {
                req.params = sanitizeObject(req.params);
            }

            // Additional schema validation can be added here
            next();
        } catch (error) {
            console.error('Input validation error:', error);
            res.status(400).json({ error: 'Invalid input data' });
        }
    };
};

/**
 * File upload security middleware
 */
const secureFileUpload = (options = {}) => {
    const config = securityConfig.get('fileUpload');
    
    return (req, res, next) => {
        // File type validation
        if (req.file) {
            const allowedTypes = options.allowedTypes || config.allowedTypes;
            const maxSize = options.maxSize || config.maxSize;
            
            if (!allowedTypes.includes(req.file.mimetype)) {
                return res.status(400).json({ 
                    error: 'File type not allowed',
                    allowed: allowedTypes 
                });
            }
            
            if (req.file.size > maxSize) {
                return res.status(400).json({ 
                    error: 'File size too large',
                    maxSize: maxSize 
                });
            }

            // Log file upload
            auditLogger.log(
                req.user?.user_id,
                'file_upload',
                'file',
                req.file.filename,
                null,
                {
                    originalName: req.file.originalname,
                    mimetype: req.file.mimetype,
                    size: req.file.size
                },
                req.ip
            );
        }
        
        next();
    };
};

/**
 * Security monitoring middleware
 */
const securityMonitoring = async (req, res, next) => {
    try {
        // Monitor for suspicious patterns
        const suspiciousPatterns = [
            /(\<script\>|\<\/script\>)/gi,
            /(union|select|insert|delete|update|drop|create|alter)/gi,
            /(\.\.|\/etc\/|\/proc\/|\/sys\/)/gi
        ];

        const requestData = JSON.stringify({
            body: req.body,
            query: req.query,
            params: req.params
        });

        for (const pattern of suspiciousPatterns) {
            if (pattern.test(requestData)) {
                await auditLogger.logSecurityEvent(
                    'suspicious_request_pattern',
                    req.user?.user_id || null,
                    req.ip,
                    {
                        pattern: pattern.toString(),
                        endpoint: req.path,
                        method: req.method,
                        userAgent: req.get('User-Agent')
                    },
                    'high'
                );
                break;
            }
        }

        next();
    } catch (error) {
        console.error('Security monitoring error:', error);
        next();
    }
};

/**
 * Helper functions
 */
async function validateSession(sessionId, ip, userAgent) {
    try {
        const db = require('../database/connection').getDatabase();
        
        return new Promise((resolve, reject) => {
            db.get(
                `SELECT * FROM user_sessions 
                 WHERE session_id = ? AND is_active = 1 AND expires_at > CURRENT_TIMESTAMP`,
                [sessionId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    } catch (error) {
        console.error('Session validation error:', error);
        return null;
    }
}

async function updateSessionActivity(sessionId) {
    try {
        const db = require('../database/connection').getDatabase();
        
        await new Promise((resolve, reject) => {
            db.run(
                'UPDATE user_sessions SET last_activity = CURRENT_TIMESTAMP WHERE session_id = ?',
                [sessionId],
                (err) => {
                    if (err) reject(err);
                    else resolve();
                }
            );
        });
    } catch (error) {
        console.error('Session activity update error:', error);
    }
}

function sanitizeObject(obj) {
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string') {
            // Basic XSS prevention
            sanitized[key] = value
                .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
                .replace(/javascript:/gi, '')
                .replace(/on\w+\s*=/gi, '');
        } else if (typeof value === 'object') {
            sanitized[key] = sanitizeObject(value);
        } else {
            sanitized[key] = value;
        }
    }
    return sanitized;
}

module.exports = {
    createRateLimiter,
    createSlowDown,
    bruteForceProtection,
    securityHeaders,
    xssProtection,
    hppProtection,
    csrfProtection,
    requireAuth,
    requirePermission,
    requireRole,
    validateInput,
    secureFileUpload,
    securityMonitoring
};
