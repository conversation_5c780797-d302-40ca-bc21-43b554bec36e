const crypto = require('crypto');
const path = require('path');

/**
 * Security Configuration Manager
 * Centralized security settings and policies
 */
class SecurityConfig {
    constructor() {
        this.configPath = path.join(__dirname, 'security-settings.json');
        this.defaultConfig = {
            // Authentication Settings
            authentication: {
                sessionTimeout: 86400, // 24 hours in seconds
                maxLoginAttempts: 5,
                lockoutDuration: 900, // 15 minutes in seconds
                passwordPolicy: {
                    minLength: 8,
                    maxLength: 128,
                    requireUppercase: true,
                    requireLowercase: true,
                    requireNumbers: true,
                    requireSpecialChars: true,
                    preventReuse: 5, // Last 5 passwords
                    maxAge: 7776000, // 90 days in seconds
                    forceChangeOnFirstLogin: true
                },
                twoFactorAuth: {
                    enabled: false,
                    issuer: 'QRSAMS',
                    window: 2, // Allow 2 time steps before/after
                    backupCodes: 10 // Number of backup codes to generate
                }
            },

            // Session Security
            session: {
                secure: false, // Set to true in production with HTTPS
                httpOnly: true,
                sameSite: 'strict',
                rolling: true, // Reset expiry on activity
                maxAge: 86400000, // 24 hours in milliseconds
                regenerateOnLogin: true,
                trackFingerprint: true
            },

            // Rate Limiting
            rateLimiting: {
                enabled: true,
                windowMs: 900000, // 15 minutes
                maxRequests: 100,
                skipSuccessfulRequests: false,
                skipFailedRequests: false,
                standardHeaders: true,
                legacyHeaders: false
            },

            // Brute Force Protection
            bruteForce: {
                enabled: true,
                freeRetries: 3,
                minWait: 5 * 60 * 1000, // 5 minutes
                maxWait: 60 * 60 * 1000, // 1 hour
                lifetime: 24 * 60 * 60, // 24 hours
                failCallback: 'block'
            },

            // CSRF Protection
            csrf: {
                enabled: true,
                cookie: {
                    httpOnly: true,
                    secure: false, // Set to true in production
                    sameSite: 'strict'
                }
            },

            // Content Security Policy
            csp: {
                enabled: true,
                directives: {
                    defaultSrc: ["'self'"],
                    styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.jsdelivr.net"],
                    scriptSrc: ["'self'", "https://cdn.jsdelivr.net"],
                    imgSrc: ["'self'", "data:", "https:"],
                    fontSrc: ["'self'", "https://cdn.jsdelivr.net"],
                    connectSrc: ["'self'"],
                    frameSrc: ["'none'"],
                    objectSrc: ["'none'"],
                    upgradeInsecureRequests: []
                }
            },

            // File Upload Security
            fileUpload: {
                maxSize: 5 * 1024 * 1024, // 5MB
                allowedMimeTypes: [
                    'image/jpeg',
                    'image/png',
                    'image/gif',
                    'image/webp',
                    'text/csv',
                    'application/vnd.ms-excel',
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                ],
                allowedExtensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.csv', '.xls', '.xlsx'],
                scanForViruses: false, // Enable if antivirus scanning is available
                quarantineSuspicious: true,
                generateUniqueNames: true,
                preserveOriginalName: false
            },

            // Data Encryption
            encryption: {
                algorithm: 'aes-256-gcm',
                keyRotationDays: 90,
                encryptSensitiveFields: true,
                sensitiveFields: [
                    'password_hash',
                    'two_factor_secret',
                    'backup_codes',
                    'parent_contact',
                    'contact_number',
                    'email'
                ]
            },

            // Audit and Logging
            audit: {
                enabled: true,
                logLevel: 'info', // debug, info, warn, error
                logSensitiveData: false,
                retentionDays: 365,
                realTimeAlerts: true,
                categories: [
                    'authentication',
                    'authorization',
                    'data_access',
                    'data_modification',
                    'security_events',
                    'system_events'
                ]
            },

            // Privacy and Compliance
            privacy: {
                dataRetentionDays: 365,
                anonymizeAfterDays: 1095, // 3 years
                requireConsent: true,
                allowDataExport: true,
                allowDataDeletion: true,
                minimizeDataCollection: true,
                pseudonymizeReports: true
            },

            // Security Monitoring
            monitoring: {
                enabled: true,
                alertThresholds: {
                    failedLogins: 10,
                    suspiciousActivity: 5,
                    dataAccess: 100,
                    systemErrors: 20
                },
                alertMethods: ['email', 'dashboard'],
                monitoringInterval: 60000, // 1 minute
                autoBlock: {
                    enabled: true,
                    suspiciousIPs: true,
                    bruteForceAttempts: true,
                    maliciousRequests: true
                }
            },

            // IP Whitelisting
            ipWhitelist: {
                enabled: false,
                allowedIPs: [],
                allowLocalNetwork: true,
                blockTor: false,
                blockVPN: false
            },

            // Security Headers
            headers: {
                hsts: {
                    enabled: false, // Enable in production with HTTPS
                    maxAge: 31536000,
                    includeSubDomains: true,
                    preload: true
                },
                xFrameOptions: 'DENY',
                xContentTypeOptions: 'nosniff',
                xXSSProtection: '1; mode=block',
                referrerPolicy: 'strict-origin-when-cross-origin',
                permissionsPolicy: 'geolocation=(), microphone=(), camera=()'
            }
        };

        this.config = this.loadConfig();
    }

    /**
     * Load security configuration from file or use defaults
     * @returns {Object} Security configuration
     */
    loadConfig() {
        try {
            const fs = require('fs');
            if (fs.existsSync(this.configPath)) {
                const fileConfig = JSON.parse(fs.readFileSync(this.configPath, 'utf8'));
                return this.mergeConfig(this.defaultConfig, fileConfig);
            }
        } catch (error) {
            console.warn('Failed to load security config, using defaults:', error.message);
        }
        return this.defaultConfig;
    }

    /**
     * Save security configuration to file
     * @param {Object} config - Configuration to save
     */
    saveConfig(config = null) {
        try {
            const fs = require('fs');
            const configToSave = config || this.config;
            fs.writeFileSync(this.configPath, JSON.stringify(configToSave, null, 2));
            return true;
        } catch (error) {
            console.error('Failed to save security config:', error.message);
            return false;
        }
    }

    /**
     * Deep merge configuration objects
     * @param {Object} target - Target configuration
     * @param {Object} source - Source configuration
     * @returns {Object} Merged configuration
     */
    mergeConfig(target, source) {
        const result = { ...target };
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.mergeConfig(target[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        return result;
    }

    /**
     * Get specific configuration section
     * @param {string} section - Configuration section name
     * @returns {Object} Configuration section
     */
    get(section) {
        return this.config[section] || {};
    }

    /**
     * Update specific configuration section
     * @param {string} section - Configuration section name
     * @param {Object} values - Values to update
     */
    update(section, values) {
        if (this.config[section]) {
            this.config[section] = { ...this.config[section], ...values };
            this.saveConfig();
        }
    }

    /**
     * Generate encryption key
     * @returns {string} Base64 encoded encryption key
     */
    generateEncryptionKey() {
        return crypto.randomBytes(32).toString('base64');
    }

    /**
     * Generate secure random string
     * @param {number} length - Length of random string
     * @returns {string} Random string
     */
    generateSecureRandom(length = 32) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Validate password against policy
     * @param {string} password - Password to validate
     * @returns {Object} Validation result
     */
    validatePassword(password) {
        const policy = this.config.authentication.passwordPolicy;
        const errors = [];

        if (password.length < policy.minLength) {
            errors.push(`Password must be at least ${policy.minLength} characters long`);
        }

        if (password.length > policy.maxLength) {
            errors.push(`Password must be no more than ${policy.maxLength} characters long`);
        }

        if (policy.requireUppercase && !/[A-Z]/.test(password)) {
            errors.push('Password must contain at least one uppercase letter');
        }

        if (policy.requireLowercase && !/[a-z]/.test(password)) {
            errors.push('Password must contain at least one lowercase letter');
        }

        if (policy.requireNumbers && !/\d/.test(password)) {
            errors.push('Password must contain at least one number');
        }

        if (policy.requireSpecialChars && !/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
            errors.push('Password must contain at least one special character');
        }

        return {
            valid: errors.length === 0,
            errors: errors
        };
    }

    /**
     * Check if IP is whitelisted
     * @param {string} ip - IP address to check
     * @returns {boolean} True if whitelisted
     */
    isIPWhitelisted(ip) {
        const whitelist = this.config.ipWhitelist;
        
        if (!whitelist.enabled) {
            return true;
        }

        // Check if IP is in allowed list
        if (whitelist.allowedIPs.includes(ip)) {
            return true;
        }

        // Check if local network access is allowed
        if (whitelist.allowLocalNetwork) {
            const localRanges = [
                /^127\./,
                /^192\.168\./,
                /^10\./,
                /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
                /^::1$/,
                /^fe80:/
            ];
            
            return localRanges.some(range => range.test(ip));
        }

        return false;
    }
}

module.exports = new SecurityConfig();
