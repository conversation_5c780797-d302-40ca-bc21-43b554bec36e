<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><%= title %> - QR-SAMS</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .security-card {
            border-left: 4px solid #dc3545;
            transition: transform 0.2s;
        }
        .security-card:hover {
            transform: translateY(-2px);
        }
        .risk-high { border-left-color: #dc3545; }
        .risk-medium { border-left-color: #ffc107; }
        .risk-low { border-left-color: #28a745; }
        .event-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
        }
        .event-item:last-child {
            border-bottom: none;
        }
        .timestamp {
            font-size: 0.85em;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <%- include('../layout') %>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2><i class="fas fa-shield-alt"></i> Security Dashboard</h2>
                    <div>
                        <a href="/security/events" class="btn btn-outline-primary me-2">
                            <i class="fas fa-exclamation-triangle"></i> Security Events
                        </a>
                        <a href="/security/audit-logs" class="btn btn-outline-secondary">
                            <i class="fas fa-list"></i> Audit Logs
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Overview Cards -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card security-card risk-high">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">High Risk Events</h6>
                                <h3 class="text-danger" id="highRiskCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-circle fa-2x text-danger"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card security-card risk-medium">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Medium Risk Events</h6>
                                <h3 class="text-warning" id="mediumRiskCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card security-card risk-low">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Active Sessions</h6>
                                <h3 class="text-success" id="activeSessionsCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-users fa-2x text-success"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card security-card">
                    <div class="card-body">
                        <div class="d-flex justify-content-between">
                            <div>
                                <h6 class="card-title text-muted">Failed Logins (24h)</h6>
                                <h3 class="text-info" id="failedLoginsCount">0</h3>
                            </div>
                            <div class="align-self-center">
                                <i class="fas fa-user-times fa-2x text-info"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Security Events and Audit Logs -->
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-exclamation-triangle"></i> Recent Security Events</h5>
                    </div>
                    <div class="card-body">
                        <% if (recentEvents && recentEvents.length > 0) { %>
                            <% recentEvents.forEach(event => { %>
                                <div class="event-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong class="text-<%= event.risk_level === 'high' ? 'danger' : event.risk_level === 'medium' ? 'warning' : 'info' %>">
                                                <%= event.event_type.replace(/_/g, ' ').toUpperCase() %>
                                            </strong>
                                            <div class="text-muted small">
                                                <% if (event.username) { %>
                                                    User: <%= event.username %>
                                                <% } %>
                                                <% if (event.ip_address) { %>
                                                    | IP: <%= event.ip_address %>
                                                <% } %>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-<%= event.risk_level === 'high' ? 'danger' : event.risk_level === 'medium' ? 'warning' : 'info' %>">
                                                <%= event.risk_level.toUpperCase() %>
                                            </span>
                                            <div class="timestamp">
                                                <%= new Date(event.timestamp).toLocaleString() %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <p class="text-muted">No recent security events</p>
                        <% } %>
                        <div class="text-center mt-3">
                            <a href="/security/events" class="btn btn-sm btn-outline-primary">View All Events</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> Recent Audit Logs</h5>
                    </div>
                    <div class="card-body">
                        <% if (recentLogs && recentLogs.length > 0) { %>
                            <% recentLogs.forEach(log => { %>
                                <div class="event-item">
                                    <div class="d-flex justify-content-between align-items-start">
                                        <div>
                                            <strong><%= log.action.replace(/_/g, ' ').toUpperCase() %></strong>
                                            <div class="text-muted small">
                                                <% if (log.username) { %>
                                                    User: <%= log.username %>
                                                <% } %>
                                                | Resource: <%= log.resource_type %>
                                                <% if (log.ip_address) { %>
                                                    | IP: <%= log.ip_address %>
                                                <% } %>
                                            </div>
                                        </div>
                                        <div class="text-end">
                                            <span class="badge bg-<%= log.severity === 'error' ? 'danger' : log.severity === 'warning' ? 'warning' : 'info' %>">
                                                <%= log.severity.toUpperCase() %>
                                            </span>
                                            <div class="timestamp">
                                                <%= new Date(log.timestamp).toLocaleString() %>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            <% }); %>
                        <% } else { %>
                            <p class="text-muted">No recent audit logs</p>
                        <% } %>
                        <div class="text-center mt-3">
                            <a href="/security/audit-logs" class="btn btn-sm btn-outline-secondary">View All Logs</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security Actions -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="fas fa-cogs"></i> Security Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <button class="btn btn-outline-primary w-100 mb-2" onclick="setup2FA()">
                                    <i class="fas fa-mobile-alt"></i><br>
                                    Setup 2FA
                                </button>
                            </div>
                            <div class="col-md-3">
                                <button class="btn btn-outline-warning w-100 mb-2" onclick="changePassword()">
                                    <i class="fas fa-key"></i><br>
                                    Change Password
                                </button>
                            </div>
                            <div class="col-md-3">
                                <a href="/security/audit-logs/export" class="btn btn-outline-info w-100 mb-2">
                                    <i class="fas fa-download"></i><br>
                                    Export Logs
                                </a>
                            </div>
                            <% if (user.role === 'admin') { %>
                            <div class="col-md-3">
                                <button class="btn btn-outline-danger w-100 mb-2" onclick="rotateKeys()">
                                    <i class="fas fa-sync-alt"></i><br>
                                    Rotate Keys
                                </button>
                            </div>
                            <% } %>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 2FA Setup Modal -->
    <div class="modal fade" id="twoFactorModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Setup Two-Factor Authentication</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="qrCodeContainer" class="text-center mb-3" style="display: none;">
                        <p>Scan this QR code with your authenticator app:</p>
                        <div id="qrCodeImage"></div>
                    </div>
                    <div id="backupCodesContainer" style="display: none;">
                        <h6>Backup Codes (Save these securely):</h6>
                        <div id="backupCodes" class="font-monospace small"></div>
                    </div>
                    <div id="verificationContainer" style="display: none;">
                        <label for="totpCode" class="form-label">Enter verification code:</label>
                        <input type="text" class="form-control" id="totpCode" placeholder="000000">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="enable2FABtn" style="display: none;" onclick="enable2FA()">Enable 2FA</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Change Password Modal -->
    <div class="modal fade" id="changePasswordModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Change Password</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="changePasswordForm">
                        <div class="mb-3">
                            <label for="currentPassword" class="form-label">Current Password</label>
                            <input type="password" class="form-control" id="currentPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="newPassword" class="form-label">New Password</label>
                            <input type="password" class="form-control" id="newPassword" required>
                        </div>
                        <div class="mb-3">
                            <label for="confirmPassword" class="form-label">Confirm New Password</label>
                            <input type="password" class="form-control" id="confirmPassword" required>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" onclick="submitPasswordChange()">Change Password</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Load security statistics
        async function loadSecurityStats() {
            try {
                // This would typically call API endpoints to get real statistics
                // For now, we'll use placeholder values
                document.getElementById('highRiskCount').textContent = '0';
                document.getElementById('mediumRiskCount').textContent = '0';
                document.getElementById('activeSessionsCount').textContent = '1';
                document.getElementById('failedLoginsCount').textContent = '0';
            } catch (error) {
                console.error('Failed to load security statistics:', error);
            }
        }

        // Setup 2FA
        async function setup2FA() {
            try {
                const response = await fetch('/security/2fa/setup', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('qrCodeImage').innerHTML = `<img src="${data.qrCode}" alt="QR Code">`;
                    document.getElementById('backupCodes').innerHTML = data.backupCodes.map(code => `<div>${code}</div>`).join('');
                    
                    document.getElementById('qrCodeContainer').style.display = 'block';
                    document.getElementById('backupCodesContainer').style.display = 'block';
                    document.getElementById('verificationContainer').style.display = 'block';
                    document.getElementById('enable2FABtn').style.display = 'block';
                    
                    new bootstrap.Modal(document.getElementById('twoFactorModal')).show();
                } else {
                    alert('Failed to setup 2FA: ' + data.error);
                }
            } catch (error) {
                alert('Failed to setup 2FA: ' + error.message);
            }
        }

        // Enable 2FA
        async function enable2FA() {
            const totpCode = document.getElementById('totpCode').value;
            
            if (!totpCode) {
                alert('Please enter the verification code');
                return;
            }
            
            try {
                const response = await fetch('/security/2fa/enable', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ totpCode })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Two-factor authentication enabled successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('twoFactorModal')).hide();
                } else {
                    alert('Failed to enable 2FA: ' + data.error);
                }
            } catch (error) {
                alert('Failed to enable 2FA: ' + error.message);
            }
        }

        // Change password
        function changePassword() {
            new bootstrap.Modal(document.getElementById('changePasswordModal')).show();
        }

        // Submit password change
        async function submitPasswordChange() {
            const currentPassword = document.getElementById('currentPassword').value;
            const newPassword = document.getElementById('newPassword').value;
            const confirmPassword = document.getElementById('confirmPassword').value;
            
            if (!currentPassword || !newPassword || !confirmPassword) {
                alert('Please fill in all fields');
                return;
            }
            
            if (newPassword !== confirmPassword) {
                alert('New passwords do not match');
                return;
            }
            
            try {
                const response = await fetch('/security/change-password', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ currentPassword, newPassword })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Password changed successfully!');
                    bootstrap.Modal.getInstance(document.getElementById('changePasswordModal')).hide();
                    document.getElementById('changePasswordForm').reset();
                } else {
                    alert('Failed to change password: ' + data.error);
                }
            } catch (error) {
                alert('Failed to change password: ' + error.message);
            }
        }

        // Rotate encryption keys (admin only)
        async function rotateKeys() {
            if (!confirm('Are you sure you want to rotate encryption keys? This is a sensitive operation.')) {
                return;
            }
            
            try {
                const response = await fetch('/security/encryption/rotate-keys', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' }
                });
                
                const data = await response.json();
                
                if (data.success) {
                    alert('Encryption keys rotated successfully!');
                } else {
                    alert('Failed to rotate keys: ' + data.error);
                }
            } catch (error) {
                alert('Failed to rotate keys: ' + error.message);
            }
        }

        // Load statistics on page load
        document.addEventListener('DOMContentLoaded', loadSecurityStats);
    </script>
</body>
</html>
