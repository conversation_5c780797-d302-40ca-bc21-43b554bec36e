{"server": {"host": "0.0.0.0", "port": 3000, "staticIP": null, "enableHTTPS": false, "sslCertPath": null, "sslKeyPath": null}, "network": {"subnet": "***********/24", "dhcpRange": {"start": "*************", "end": "*************"}, "gateway": "***********", "dns": ["*******", "*******"], "wifiSSID": "QRSAMS-School", "wifiPassword": null, "wifiChannel": 6, "wifiSecurity": "WPA2"}, "cors": {"allowedOrigins": ["http://localhost:3000", "http://127.0.0.1:3000", "http://192.168.1.*", "http://192.168.0.*", "http://10.0.0.*"], "allowCredentials": true}, "offline": {"enableOfflineMode": true, "syncInterval": 300000, "maxOfflineRecords": 10000, "offlineStoragePath": "./data/offline"}, "monitoring": {"enableHealthChecks": true, "healthCheckInterval": 30000, "enablePerformanceMonitoring": true, "logLevel": "info"}, "backup": {"autoBackup": true, "backupPath": "./backups", "backupInterval": 86400000, "retentionDays": 30, "includeLogsInBackup": false, "compressionLevel": 9}}