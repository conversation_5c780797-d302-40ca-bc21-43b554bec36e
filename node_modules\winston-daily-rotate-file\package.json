{"name": "winston-daily-rotate-file", "version": "5.0.0", "description": "A transport for winston which logs to a rotating file each day.", "main": "index.js", "types": "index.d.ts", "engines": {"node": ">=8"}, "scripts": {"test": "mocha --ignore **/*.worker.js && eslint .", "release": "release-script"}, "repository": {"type": "git", "url": "**************:winstonjs/winston-daily-rotate-file.git"}, "keywords": ["winston", "daily-rotate-file", "log-rotate", "logrotate"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/winstonjs/winston-daily-rotate-file/issues"}, "files": ["daily-rotate-file.js", "index.js", "index.d.ts"], "homepage": "https://github.com/winstonjs/winston-daily-rotate-file#readme", "peerDependencies": {"winston": "^3"}, "devDependencies": {"@alcalzone/release-script": "^3.7.0", "@alcalzone/release-script-plugin-license": "^3.7.0", "chai": "^4.4.1", "eslint": "^8.56.0", "eslint-plugin-node": "^11.1.0", "mocha": "^10.2.0", "rimraf": "^5.0.5", "threads": "^1.7.0"}, "dependencies": {"file-stream-rotator": "^0.6.1", "object-hash": "^3.0.0", "triple-beam": "^1.4.1", "winston-transport": "^4.7.0"}}