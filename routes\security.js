const express = require('express');
const router = express.Router();
const authService = require('../services/auth-service');
const permissionService = require('../services/permission-service');
const auditLogger = require('../services/audit-logger');
const encryptionService = require('../services/encryption-service');
const { requireAuth, requirePermission, requireRole } = require('../middleware/security');

/**
 * Security Management Routes
 * Handles security settings, audit logs, and security events
 */

// Get security dashboard
router.get('/dashboard', requireAuth, requirePermission('security.view_events'), async (req, res) => {
    try {
        const recentEvents = await auditLogger.getSecurityEvents({}, 10);
        const recentLogs = await auditLogger.getAuditLogs({}, 10);
        
        res.render('security/dashboard', {
            title: 'Security Dashboard',
            user: req.user,
            recentEvents,
            recentLogs
        });
    } catch (error) {
        console.error('Security dashboard error:', error);
        res.status(500).json({ error: 'Failed to load security dashboard' });
    }
});

// Get audit logs
router.get('/audit-logs', requireAuth, requirePermission('audit.view_logs'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const offset = (page - 1) * limit;
        
        const filters = {
            userId: req.query.userId,
            action: req.query.action,
            resourceType: req.query.resourceType,
            severity: req.query.severity,
            category: req.query.category,
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            ip: req.query.ip
        };
        
        // Remove empty filters
        Object.keys(filters).forEach(key => {
            if (!filters[key]) delete filters[key];
        });
        
        const logs = await auditLogger.getAuditLogs(filters, limit, offset);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            res.json({
                success: true,
                logs,
                pagination: {
                    page,
                    limit,
                    hasMore: logs.length === limit
                }
            });
        } else {
            res.render('security/audit-logs', {
                title: 'Audit Logs',
                user: req.user,
                logs,
                filters: req.query,
                pagination: { page, limit, hasMore: logs.length === limit }
            });
        }
    } catch (error) {
        console.error('Audit logs error:', error);
        res.status(500).json({ error: 'Failed to retrieve audit logs' });
    }
});

// Get security events
router.get('/events', requireAuth, requirePermission('security.view_events'), async (req, res) => {
    try {
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 50;
        const offset = (page - 1) * limit;
        
        const filters = {
            eventType: req.query.eventType,
            riskLevel: req.query.riskLevel,
            resolved: req.query.resolved === 'true' ? true : req.query.resolved === 'false' ? false : undefined,
            startDate: req.query.startDate,
            endDate: req.query.endDate
        };
        
        // Remove empty filters
        Object.keys(filters).forEach(key => {
            if (filters[key] === undefined || filters[key] === '') delete filters[key];
        });
        
        const events = await auditLogger.getSecurityEvents(filters, limit, offset);
        
        if (req.headers.accept && req.headers.accept.includes('application/json')) {
            res.json({
                success: true,
                events,
                pagination: {
                    page,
                    limit,
                    hasMore: events.length === limit
                }
            });
        } else {
            res.render('security/events', {
                title: 'Security Events',
                user: req.user,
                events,
                filters: req.query,
                pagination: { page, limit, hasMore: events.length === limit }
            });
        }
    } catch (error) {
        console.error('Security events error:', error);
        res.status(500).json({ error: 'Failed to retrieve security events' });
    }
});

// Export audit logs
router.get('/audit-logs/export', requireAuth, requirePermission('audit.export_logs'), async (req, res) => {
    try {
        const filters = {
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            userId: req.query.userId,
            action: req.query.action,
            resourceType: req.query.resourceType
        };
        
        // Remove empty filters
        Object.keys(filters).forEach(key => {
            if (!filters[key]) delete filters[key];
        });
        
        const logs = await auditLogger.getAuditLogs(filters, 10000); // Large limit for export
        
        // Convert to CSV
        const csvHeaders = [
            'Timestamp', 'User', 'Action', 'Resource Type', 'Resource ID',
            'IP Address', 'Severity', 'Category', 'Old Values', 'New Values'
        ];
        
        const csvRows = logs.map(log => [
            log.timestamp,
            log.username || 'System',
            log.action,
            log.resource_type,
            log.resource_id || '',
            log.ip_address || '',
            log.severity,
            log.category,
            log.old_values || '',
            log.new_values || ''
        ]);
        
        const csvContent = [csvHeaders, ...csvRows]
            .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
            .join('\n');
        
        // Log the export
        await auditLogger.log(
            req.user.user_id,
            'audit_logs_exported',
            'audit',
            'export',
            null,
            { filters, recordCount: logs.length },
            req.ip,
            null,
            req.session?.session_id
        );
        
        res.setHeader('Content-Type', 'text/csv');
        res.setHeader('Content-Disposition', `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`);
        res.send(csvContent);
        
    } catch (error) {
        console.error('Audit logs export error:', error);
        res.status(500).json({ error: 'Failed to export audit logs' });
    }
});

// Get user permissions
router.get('/permissions/:userId', requireAuth, requirePermission('users.view'), async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const permissions = await permissionService.getUserPermissions(userId);
        
        res.json({
            success: true,
            permissions
        });
    } catch (error) {
        console.error('Get user permissions error:', error);
        res.status(500).json({ error: 'Failed to retrieve user permissions' });
    }
});

// Grant user permission
router.post('/permissions/:userId/grant', requireAuth, requirePermission('users.manage_roles'), async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const { permissionName, reason, expiresAt } = req.body;
        
        if (!permissionName) {
            return res.status(400).json({ error: 'Permission name is required' });
        }
        
        const expirationDate = expiresAt ? new Date(expiresAt) : null;
        
        const success = await permissionService.grantUserPermission(
            userId,
            permissionName,
            req.user.user_id,
            expirationDate,
            reason
        );
        
        if (success) {
            res.json({ success: true, message: 'Permission granted successfully' });
        } else {
            res.status(400).json({ error: 'Failed to grant permission' });
        }
    } catch (error) {
        console.error('Grant permission error:', error);
        res.status(500).json({ error: 'Failed to grant permission' });
    }
});

// Revoke user permission
router.post('/permissions/:userId/revoke', requireAuth, requirePermission('users.manage_roles'), async (req, res) => {
    try {
        const userId = parseInt(req.params.userId);
        const { permissionName, reason } = req.body;
        
        if (!permissionName) {
            return res.status(400).json({ error: 'Permission name is required' });
        }
        
        const success = await permissionService.revokeUserPermission(
            userId,
            permissionName,
            req.user.user_id,
            reason
        );
        
        if (success) {
            res.json({ success: true, message: 'Permission revoked successfully' });
        } else {
            res.status(400).json({ error: 'Failed to revoke permission' });
        }
    } catch (error) {
        console.error('Revoke permission error:', error);
        res.status(500).json({ error: 'Failed to revoke permission' });
    }
});

// Setup 2FA
router.post('/2fa/setup', requireAuth, async (req, res) => {
    try {
        const setupInfo = await authService.setupTwoFactor(req.user.user_id);
        
        res.json({
            success: true,
            qrCode: setupInfo.qrCode,
            backupCodes: setupInfo.backupCodes,
            message: 'Two-factor authentication setup initiated. Please scan the QR code and verify with a TOTP code.'
        });
    } catch (error) {
        console.error('2FA setup error:', error);
        res.status(500).json({ error: 'Failed to setup two-factor authentication' });
    }
});

// Enable 2FA
router.post('/2fa/enable', requireAuth, async (req, res) => {
    try {
        const { totpCode } = req.body;
        
        if (!totpCode) {
            return res.status(400).json({ error: 'TOTP code is required' });
        }
        
        const success = await authService.enableTwoFactor(req.user.user_id, totpCode);
        
        if (success) {
            res.json({ success: true, message: 'Two-factor authentication enabled successfully' });
        } else {
            res.status(400).json({ error: 'Invalid TOTP code' });
        }
    } catch (error) {
        console.error('2FA enable error:', error);
        res.status(500).json({ error: 'Failed to enable two-factor authentication' });
    }
});

// Disable 2FA
router.post('/2fa/disable', requireAuth, async (req, res) => {
    try {
        const { password } = req.body;
        
        if (!password) {
            return res.status(400).json({ error: 'Password is required' });
        }
        
        const success = await authService.disableTwoFactor(req.user.user_id, password);
        
        if (success) {
            res.json({ success: true, message: 'Two-factor authentication disabled successfully' });
        } else {
            res.status(400).json({ error: 'Invalid password' });
        }
    } catch (error) {
        console.error('2FA disable error:', error);
        res.status(500).json({ error: 'Failed to disable two-factor authentication' });
    }
});

// Change password
router.post('/change-password', requireAuth, async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        
        if (!currentPassword || !newPassword) {
            return res.status(400).json({ error: 'Current password and new password are required' });
        }
        
        const result = await authService.changePassword(req.user.user_id, currentPassword, newPassword);
        
        if (result.success) {
            res.json({ success: true, message: 'Password changed successfully' });
        } else {
            res.status(400).json({ error: result.error });
        }
    } catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ error: 'Failed to change password' });
    }
});

// Rotate encryption keys (admin only)
router.post('/encryption/rotate-keys', requireAuth, requireRole(['admin']), async (req, res) => {
    try {
        const success = await encryptionService.rotateKeys();
        
        if (success) {
            await auditLogger.log(
                req.user.user_id,
                'encryption_key_rotated',
                'system',
                'encryption',
                null,
                { timestamp: new Date().toISOString() },
                req.ip,
                null,
                req.session?.session_id,
                'info',
                'security'
            );
            
            res.json({ success: true, message: 'Encryption keys rotated successfully' });
        } else {
            res.status(500).json({ error: 'Failed to rotate encryption keys' });
        }
    } catch (error) {
        console.error('Key rotation error:', error);
        res.status(500).json({ error: 'Failed to rotate encryption keys' });
    }
});

module.exports = router;
