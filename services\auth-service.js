const bcrypt = require('bcrypt');
const crypto = require('crypto');
const speakeasy = require('speakeasy');
const QRCode = require('qrcode');
const dbConnection = require('../database/connection');
const securityConfig = require('../config/security-config');
const auditLogger = require('./audit-logger');
const encryptionService = require('./encryption-service');

/**
 * Enhanced Authentication Service
 * Handles user authentication, 2FA, password policies, and session management
 */
class AuthService {
    constructor() {
        this.db = dbConnection.getDatabase();
        this.config = securityConfig.get('authentication');
        this.sessionConfig = securityConfig.get('session');
    }

    /**
     * Authenticate user with enhanced security checks
     * @param {string} username - Username
     * @param {string} password - Password
     * @param {string} totpCode - TOTP code for 2FA (optional)
     * @param {Object} requestInfo - Request information (IP, user agent, etc.)
     * @returns {Promise<Object>} Authentication result
     */
    async authenticate(username, password, totpCode = null, requestInfo = {}) {
        const { ip, userAgent, deviceFingerprint } = requestInfo;
        
        try {
            // Check for account lockout
            const lockoutCheck = await this.checkAccountLockout(username, ip);
            if (lockoutCheck.locked) {
                await this.logSecurityEvent('account_lockout_attempt', null, ip, {
                    username,
                    reason: lockoutCheck.reason
                });
                return {
                    success: false,
                    error: 'Account is temporarily locked',
                    lockoutUntil: lockoutCheck.lockoutUntil
                };
            }

            // Find user
            const user = await this.findUserByUsername(username);
            if (!user) {
                await this.recordFailedLogin(username, ip, 'user_not_found');
                return { success: false, error: 'Invalid credentials' };
            }

            // Check account status
            if (user.account_status !== 'active') {
                await this.logSecurityEvent('inactive_account_login', user.user_id, ip, {
                    username,
                    status: user.account_status
                });
                return { success: false, error: 'Account is not active' };
            }

            // Verify password
            const passwordValid = await bcrypt.compare(password, user.password_hash);
            if (!passwordValid) {
                await this.recordFailedLogin(username, ip, 'invalid_password', user.user_id);
                return { success: false, error: 'Invalid credentials' };
            }

            // Check 2FA if enabled
            if (user.two_factor_enabled) {
                if (!totpCode) {
                    return { 
                        success: false, 
                        error: 'Two-factor authentication required',
                        requiresTwoFactor: true 
                    };
                }

                const twoFactorValid = await this.verifyTwoFactor(user, totpCode);
                if (!twoFactorValid) {
                    await this.recordFailedLogin(username, ip, 'invalid_2fa', user.user_id);
                    return { success: false, error: 'Invalid two-factor code' };
                }
            }

            // Check password expiry
            const passwordExpired = await this.isPasswordExpired(user);
            if (passwordExpired) {
                return {
                    success: false,
                    error: 'Password has expired',
                    requiresPasswordChange: true,
                    userId: user.user_id
                };
            }

            // Check if password change is forced
            if (user.force_password_change) {
                return {
                    success: false,
                    error: 'Password change required',
                    requiresPasswordChange: true,
                    userId: user.user_id
                };
            }

            // Clear failed login attempts
            await this.clearFailedLoginAttempts(username, ip);

            // Update last login
            await this.updateLastLogin(user.user_id, ip);

            // Create session
            const session = await this.createSession(user, ip, userAgent, deviceFingerprint);

            // Log successful login
            await auditLogger.log(user.user_id, 'login', 'user', user.user_id, null, null, ip);

            return {
                success: true,
                user: this.sanitizeUser(user),
                session: session
            };

        } catch (error) {
            console.error('Authentication error:', error);
            await this.logSecurityEvent('authentication_error', null, ip, {
                username,
                error: error.message
            });
            return { success: false, error: 'Authentication failed' };
        }
    }

    /**
     * Setup two-factor authentication for user
     * @param {number} userId - User ID
     * @returns {Promise<Object>} 2FA setup information
     */
    async setupTwoFactor(userId) {
        try {
            const user = await this.findUserById(userId);
            if (!user) {
                throw new Error('User not found');
            }

            // Generate secret
            const secret = speakeasy.generateSecret({
                name: `QRSAMS (${user.username})`,
                issuer: this.config.twoFactorAuth.issuer,
                length: 32
            });

            // Generate backup codes
            const backupCodes = this.generateBackupCodes();

            // Store encrypted secret and backup codes
            const encryptedSecret = await encryptionService.encrypt(secret.base32);
            const encryptedBackupCodes = await encryptionService.encrypt(JSON.stringify(backupCodes));

            await this.db.run(
                `UPDATE users SET 
                 two_factor_secret = ?, 
                 backup_codes = ?,
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE user_id = ?`,
                [encryptedSecret, encryptedBackupCodes, userId]
            );

            // Generate QR code
            const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

            await auditLogger.log(userId, '2fa_setup_initiated', 'user', userId, null, null);

            return {
                secret: secret.base32,
                qrCode: qrCodeUrl,
                backupCodes: backupCodes
            };

        } catch (error) {
            console.error('2FA setup error:', error);
            throw error;
        }
    }

    /**
     * Enable two-factor authentication
     * @param {number} userId - User ID
     * @param {string} totpCode - TOTP verification code
     * @returns {Promise<boolean>} Success status
     */
    async enableTwoFactor(userId, totpCode) {
        try {
            const user = await this.findUserById(userId);
            if (!user || !user.two_factor_secret) {
                return false;
            }

            // Verify the TOTP code
            const secret = await encryptionService.decrypt(user.two_factor_secret);
            const verified = speakeasy.totp.verify({
                secret: secret,
                encoding: 'base32',
                token: totpCode,
                window: this.config.twoFactorAuth.window
            });

            if (!verified) {
                return false;
            }

            // Enable 2FA
            await this.db.run(
                `UPDATE users SET 
                 two_factor_enabled = 1,
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE user_id = ?`,
                [userId]
            );

            await auditLogger.log(userId, '2fa_enabled', 'user', userId, null, null);
            return true;

        } catch (error) {
            console.error('2FA enable error:', error);
            return false;
        }
    }

    /**
     * Disable two-factor authentication
     * @param {number} userId - User ID
     * @param {string} password - Current password for verification
     * @returns {Promise<boolean>} Success status
     */
    async disableTwoFactor(userId, password) {
        try {
            const user = await this.findUserById(userId);
            if (!user) {
                return false;
            }

            // Verify password
            const passwordValid = await bcrypt.compare(password, user.password_hash);
            if (!passwordValid) {
                return false;
            }

            // Disable 2FA
            await this.db.run(
                `UPDATE users SET 
                 two_factor_enabled = 0,
                 two_factor_secret = NULL,
                 backup_codes = NULL,
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE user_id = ?`,
                [userId]
            );

            await auditLogger.log(userId, '2fa_disabled', 'user', userId, null, null);
            return true;

        } catch (error) {
            console.error('2FA disable error:', error);
            return false;
        }
    }

    /**
     * Change user password with policy enforcement
     * @param {number} userId - User ID
     * @param {string} currentPassword - Current password
     * @param {string} newPassword - New password
     * @returns {Promise<Object>} Change result
     */
    async changePassword(userId, currentPassword, newPassword) {
        try {
            const user = await this.findUserById(userId);
            if (!user) {
                return { success: false, error: 'User not found' };
            }

            // Verify current password (unless forced change)
            if (!user.force_password_change) {
                const passwordValid = await bcrypt.compare(currentPassword, user.password_hash);
                if (!passwordValid) {
                    return { success: false, error: 'Current password is incorrect' };
                }
            }

            // Validate new password
            const validation = securityConfig.validatePassword(newPassword);
            if (!validation.valid) {
                return { success: false, error: validation.errors.join(', ') };
            }

            // Check password history
            const reused = await this.isPasswordReused(userId, newPassword);
            if (reused) {
                return { 
                    success: false, 
                    error: `Password cannot be one of the last ${this.config.passwordPolicy.preventReuse} passwords` 
                };
            }

            // Hash new password
            const newPasswordHash = await bcrypt.hash(newPassword, 12);

            // Update password
            await this.db.run(
                `UPDATE users SET 
                 password_hash = ?,
                 password_changed_at = CURRENT_TIMESTAMP,
                 force_password_change = 0,
                 failed_login_attempts = 0,
                 account_locked_until = NULL,
                 updated_at = CURRENT_TIMESTAMP 
                 WHERE user_id = ?`,
                [newPasswordHash, userId]
            );

            // Store in password history
            await this.addToPasswordHistory(userId, newPasswordHash);

            // Invalidate all sessions except current
            await this.invalidateUserSessions(userId, true);

            await auditLogger.log(userId, 'password_changed', 'user', userId, null, null);

            return { success: true };

        } catch (error) {
            console.error('Password change error:', error);
            return { success: false, error: 'Password change failed' };
        }
    }

    /**
     * Generate backup codes for 2FA
     * @returns {Array<string>} Array of backup codes
     */
    generateBackupCodes() {
        const codes = [];
        for (let i = 0; i < this.config.twoFactorAuth.backupCodes; i++) {
            codes.push(crypto.randomBytes(4).toString('hex').toUpperCase());
        }
        return codes;
    }

    /**
     * Verify two-factor authentication code
     * @param {Object} user - User object
     * @param {string} code - TOTP or backup code
     * @returns {Promise<boolean>} Verification result
     */
    async verifyTwoFactor(user, code) {
        try {
            if (!user.two_factor_secret) {
                return false;
            }

            const secret = await encryptionService.decrypt(user.two_factor_secret);

            // Try TOTP first
            const totpValid = speakeasy.totp.verify({
                secret: secret,
                encoding: 'base32',
                token: code,
                window: this.config.twoFactorAuth.window
            });

            if (totpValid) {
                return true;
            }

            // Try backup codes
            if (user.backup_codes) {
                const backupCodes = JSON.parse(await encryptionService.decrypt(user.backup_codes));
                const codeIndex = backupCodes.indexOf(code.toUpperCase());
                
                if (codeIndex !== -1) {
                    // Remove used backup code
                    backupCodes.splice(codeIndex, 1);
                    const encryptedCodes = await encryptionService.encrypt(JSON.stringify(backupCodes));
                    
                    await this.db.run(
                        'UPDATE users SET backup_codes = ? WHERE user_id = ?',
                        [encryptedCodes, user.user_id]
                    );

                    await auditLogger.log(user.user_id, 'backup_code_used', 'user', user.user_id, null, null);
                    return true;
                }
            }

            return false;

        } catch (error) {
            console.error('2FA verification error:', error);
            return false;
        }
    }

    /**
     * Check account lockout status
     * @param {string} username - Username
     * @param {string} ip - IP address
     * @returns {Promise<Object>} Lockout status
     */
    async checkAccountLockout(username, ip) {
        try {
            // Check user-specific lockout
            const user = await this.findUserByUsername(username);
            if (user && user.account_locked_until) {
                const lockoutUntil = new Date(user.account_locked_until);
                if (lockoutUntil > new Date()) {
                    return {
                        locked: true,
                        reason: 'account_locked',
                        lockoutUntil: lockoutUntil
                    };
                }
            }

            // Check IP-based lockout
            const recentAttempts = await this.getRecentFailedAttempts(ip);
            if (recentAttempts >= this.config.maxLoginAttempts) {
                return {
                    locked: true,
                    reason: 'too_many_attempts',
                    lockoutUntil: new Date(Date.now() + this.config.lockoutDuration * 1000)
                };
            }

            return { locked: false };
        } catch (error) {
            console.error('Lockout check error:', error);
            return { locked: false };
        }
    }

    /**
     * Record failed login attempt
     * @param {string} username - Username
     * @param {string} ip - IP address
     * @param {string} reason - Failure reason
     * @param {number} userId - User ID (optional)
     */
    async recordFailedLogin(username, ip, reason, userId = null) {
        try {
            // Record in login attempts table
            await this.db.run(
                `INSERT INTO login_attempts (username, ip_address, success, failure_reason, timestamp)
                 VALUES (?, ?, 0, ?, CURRENT_TIMESTAMP)`,
                [username, ip, reason]
            );

            // Update user failed attempts if user exists
            if (userId) {
                await this.db.run(
                    `UPDATE users SET
                     failed_login_attempts = failed_login_attempts + 1,
                     updated_at = CURRENT_TIMESTAMP
                     WHERE user_id = ?`,
                    [userId]
                );

                // Check if account should be locked
                const user = await this.findUserById(userId);
                if (user && user.failed_login_attempts + 1 >= this.config.maxLoginAttempts) {
                    const lockoutUntil = new Date(Date.now() + this.config.lockoutDuration * 1000);
                    await this.db.run(
                        'UPDATE users SET account_locked_until = ? WHERE user_id = ?',
                        [lockoutUntil.toISOString(), userId]
                    );

                    await this.logSecurityEvent('account_locked', userId, ip, {
                        username,
                        reason: 'max_failed_attempts'
                    });
                }
            }

            await this.logSecurityEvent('failed_login', userId, ip, {
                username,
                reason
            });

        } catch (error) {
            console.error('Failed login recording error:', error);
        }
    }

    /**
     * Clear failed login attempts
     * @param {string} username - Username
     * @param {string} ip - IP address
     */
    async clearFailedLoginAttempts(username, ip) {
        try {
            const user = await this.findUserByUsername(username);
            if (user) {
                await this.db.run(
                    `UPDATE users SET
                     failed_login_attempts = 0,
                     account_locked_until = NULL,
                     updated_at = CURRENT_TIMESTAMP
                     WHERE user_id = ?`,
                    [user.user_id]
                );
            }
        } catch (error) {
            console.error('Clear failed attempts error:', error);
        }
    }

    /**
     * Get recent failed attempts for IP
     * @param {string} ip - IP address
     * @returns {Promise<number>} Number of recent failed attempts
     */
    async getRecentFailedAttempts(ip) {
        try {
            const cutoff = new Date(Date.now() - this.config.lockoutDuration * 1000);
            const result = await new Promise((resolve, reject) => {
                this.db.get(
                    `SELECT COUNT(*) as count FROM login_attempts
                     WHERE ip_address = ? AND success = 0 AND timestamp > ?`,
                    [ip, cutoff.toISOString()],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });
            return result.count || 0;
        } catch (error) {
            console.error('Get failed attempts error:', error);
            return 0;
        }
    }

    /**
     * Update last login timestamp
     * @param {number} userId - User ID
     * @param {string} ip - IP address
     */
    async updateLastLogin(userId, ip) {
        try {
            await this.db.run(
                'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE user_id = ?',
                [userId]
            );
        } catch (error) {
            console.error('Update last login error:', error);
        }
    }

    /**
     * Check if password is expired
     * @param {Object} user - User object
     * @returns {boolean} True if password is expired
     */
    async isPasswordExpired(user) {
        if (!user.password_changed_at || !this.config.passwordPolicy.maxAge) {
            return false;
        }

        const passwordAge = Date.now() - new Date(user.password_changed_at).getTime();
        return passwordAge > (this.config.passwordPolicy.maxAge * 1000);
    }

    /**
     * Check if password was recently used
     * @param {number} userId - User ID
     * @param {string} password - Password to check
     * @returns {Promise<boolean>} True if password was reused
     */
    async isPasswordReused(userId, password) {
        try {
            const history = await new Promise((resolve, reject) => {
                this.db.all(
                    `SELECT password_hash FROM password_history
                     WHERE user_id = ?
                     ORDER BY created_at DESC
                     LIMIT ?`,
                    [userId, this.config.passwordPolicy.preventReuse],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows);
                    }
                );
            });

            for (const record of history) {
                if (await bcrypt.compare(password, record.password_hash)) {
                    return true;
                }
            }

            return false;
        } catch (error) {
            console.error('Password reuse check error:', error);
            return false;
        }
    }

    /**
     * Add password to history
     * @param {number} userId - User ID
     * @param {string} passwordHash - Password hash
     */
    async addToPasswordHistory(userId, passwordHash) {
        try {
            await this.db.run(
                'INSERT INTO password_history (user_id, password_hash) VALUES (?, ?)',
                [userId, passwordHash]
            );

            // Clean up old history entries
            await this.db.run(
                `DELETE FROM password_history
                 WHERE user_id = ? AND history_id NOT IN (
                     SELECT history_id FROM password_history
                     WHERE user_id = ?
                     ORDER BY created_at DESC
                     LIMIT ?
                 )`,
                [userId, userId, this.config.passwordPolicy.preventReuse]
            );
        } catch (error) {
            console.error('Password history error:', error);
        }
    }

    /**
     * Find user by username
     * @param {string} username - Username
     * @returns {Promise<Object|null>} User object or null
     */
    async findUserByUsername(username) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE username = ?',
                [username],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    /**
     * Find user by ID
     * @param {number} userId - User ID
     * @returns {Promise<Object|null>} User object or null
     */
    async findUserById(userId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE user_id = ?',
                [userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    /**
     * Create user session
     * @param {Object} user - User object
     * @param {string} ip - IP address
     * @param {string} userAgent - User agent
     * @param {string} deviceFingerprint - Device fingerprint
     * @returns {Promise<Object>} Session object
     */
    async createSession(user, ip, userAgent, deviceFingerprint) {
        try {
            const sessionId = crypto.randomBytes(32).toString('hex');
            const expiresAt = new Date(Date.now() + this.sessionConfig.maxAge);

            await this.db.run(
                `INSERT INTO user_sessions
                 (session_id, user_id, ip_address, user_agent, expires_at, device_fingerprint)
                 VALUES (?, ?, ?, ?, ?, ?)`,
                [sessionId, user.user_id, ip, userAgent, expiresAt.toISOString(), deviceFingerprint]
            );

            return {
                sessionId,
                expiresAt,
                userId: user.user_id
            };
        } catch (error) {
            console.error('Session creation error:', error);
            throw error;
        }
    }

    /**
     * Invalidate user sessions
     * @param {number} userId - User ID
     * @param {boolean} keepCurrent - Keep current session
     * @param {string} currentSessionId - Current session ID
     */
    async invalidateUserSessions(userId, keepCurrent = false, currentSessionId = null) {
        try {
            let query = 'UPDATE user_sessions SET is_active = 0 WHERE user_id = ?';
            let params = [userId];

            if (keepCurrent && currentSessionId) {
                query += ' AND session_id != ?';
                params.push(currentSessionId);
            }

            await this.db.run(query, params);
        } catch (error) {
            console.error('Session invalidation error:', error);
        }
    }

    /**
     * Log security event
     * @param {string} eventType - Event type
     * @param {number} userId - User ID
     * @param {string} ip - IP address
     * @param {Object} eventData - Event data
     */
    async logSecurityEvent(eventType, userId, ip, eventData) {
        try {
            await this.db.run(
                `INSERT INTO security_events
                 (event_type, user_id, ip_address, event_data, timestamp)
                 VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                [eventType, userId, ip, JSON.stringify(eventData)]
            );
        } catch (error) {
            console.error('Security event logging error:', error);
        }
    }

    /**
     * Sanitize user object for client response
     * @param {Object} user - User object
     * @returns {Object} Sanitized user object
     */
    sanitizeUser(user) {
        const { password_hash, two_factor_secret, backup_codes, reset_token, ...sanitized } = user;
        return sanitized;
    }
}

module.exports = new AuthService();
