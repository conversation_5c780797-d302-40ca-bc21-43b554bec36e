-- QR-Code Based Student Attendance and Monitoring System Database Schema
-- SQLite Database Schema with proper indexing

-- Users table for system authentication with enhanced security
CREATE TABLE IF NOT EXISTS users (
    user_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'teacher' CHECK (role IN ('admin', 'teacher', 'principal', 'staff')),
    full_name VA<PERSON><PERSON><PERSON>(100) NOT NULL,
    contact_number VARCHAR(20),
    email VARCHAR(100),
    reset_token VARCHAR(255),
    reset_token_expires DATETIME,
    last_login DATETIME,
    failed_login_attempts INTEGER DEFAULT 0,
    account_locked_until DATETIME,
    password_changed_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    force_password_change BOOLEAN DEFAULT 0,
    two_factor_enabled BOOLEAN DEFAULT 0,
    two_factor_secret VARCHAR(255),
    backup_codes TEXT, -- JSON array of backup codes
    last_password_hashes TEXT, -- JSON array of last 5 password hashes
    login_ip_whitelist TEXT, -- J<PERSON>N array of allowed IP addresses
    session_timeout INTEGER DEFAULT 86400, -- Session timeout in seconds
    account_status VARCHAR(20) DEFAULT 'active' CHECK (account_status IN ('active', 'inactive', 'suspended', 'locked')),
    privacy_consent BOOLEAN DEFAULT 0,
    privacy_consent_date DATETIME,
    data_retention_days INTEGER DEFAULT 365,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Sessions table for session management
CREATE TABLE IF NOT EXISTS sessions (
    sid VARCHAR(255) PRIMARY KEY,
    sess TEXT NOT NULL,
    expire DATETIME NOT NULL
);

-- Students table for student information
CREATE TABLE IF NOT EXISTS students (
    student_id INTEGER PRIMARY KEY AUTOINCREMENT,
    lrn VARCHAR(20) UNIQUE NOT NULL, -- Learner Reference Number
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    grade_level VARCHAR(10) NOT NULL,
    section VARCHAR(20) NOT NULL,
    parent_contact VARCHAR(20) NOT NULL,
    enrollment_status VARCHAR(20) DEFAULT 'active' CHECK (enrollment_status IN ('active', 'inactive', 'transferred', 'graduated')),
    photo_path VARCHAR(255),
    qr_code_data TEXT UNIQUE NOT NULL, -- QR code content for student identification
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Subjects table for academic subjects
CREATE TABLE IF NOT EXISTS subjects (
    subject_id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_code VARCHAR(20) UNIQUE NOT NULL,
    subject_name VARCHAR(100) NOT NULL,
    grade_level VARCHAR(10) NOT NULL,
    teacher_id INTEGER NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Class sessions table for attendance sessions
CREATE TABLE IF NOT EXISTS class_sessions (
    session_id INTEGER PRIMARY KEY AUTOINCREMENT,
    subject_id INTEGER NOT NULL,
    teacher_id INTEGER NOT NULL,
    room_number VARCHAR(20),
    date_time DATETIME NOT NULL,
    qr_code_data TEXT UNIQUE NOT NULL, -- QR code for this specific session
    qr_expiry DATETIME NOT NULL, -- When the QR code expires
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'completed')),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (subject_id) REFERENCES subjects(subject_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Attendance records table
CREATE TABLE IF NOT EXISTS attendance (
    attendance_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    session_id INTEGER NOT NULL,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'present' CHECK (status IN ('present', 'late', 'absent')),
    teacher_id INTEGER NOT NULL,
    remarks TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES class_sessions(session_id) ON DELETE CASCADE,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(student_id, session_id) -- Prevent duplicate attendance records
);

-- SMS configuration table for system-wide SMS settings
CREATE TABLE IF NOT EXISTS sms_config (
    config_id INTEGER PRIMARY KEY AUTOINCREMENT,
    textbee_server_url VARCHAR(255) NOT NULL DEFAULT 'http://localhost:8080',
    textbee_api_key VARCHAR(255),
    default_sender_name VARCHAR(50) DEFAULT 'School',
    max_retry_attempts INTEGER DEFAULT 3,
    retry_delay_minutes INTEGER DEFAULT 5,
    queue_batch_size INTEGER DEFAULT 10,
    enable_sms BOOLEAN DEFAULT 1,
    enable_delivery_reports BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- SMS message templates for different notification types
CREATE TABLE IF NOT EXISTS sms_templates (
    template_id INTEGER PRIMARY KEY AUTOINCREMENT,
    template_name VARCHAR(100) NOT NULL UNIQUE,
    template_type VARCHAR(50) NOT NULL CHECK (template_type IN ('attendance', 'absence', 'late', 'announcement', 'emergency')),
    message_template TEXT NOT NULL,
    variables TEXT, -- JSON array of available variables
    is_active BOOLEAN DEFAULT 1,
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- SMS queue for managing message delivery
CREATE TABLE IF NOT EXISTS sms_queue (
    queue_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    parent_contact VARCHAR(20) NOT NULL,
    message_content TEXT NOT NULL,
    template_id INTEGER,
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10), -- 1 = highest, 10 = lowest
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'sent', 'failed', 'cancelled')),
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 3,
    scheduled_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    processed_at DATETIME,
    teacher_id INTEGER,
    session_id INTEGER,
    attendance_id INTEGER,
    error_message TEXT,
    textbee_message_id VARCHAR(100),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES sms_templates(template_id) ON DELETE SET NULL,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES class_sessions(session_id) ON DELETE SET NULL,
    FOREIGN KEY (attendance_id) REFERENCES attendance(attendance_id) ON DELETE SET NULL
);

-- SMS logs table for parent notifications (enhanced)
CREATE TABLE IF NOT EXISTS sms_logs (
    sms_id INTEGER PRIMARY KEY AUTOINCREMENT,
    queue_id INTEGER,
    student_id INTEGER NOT NULL,
    parent_contact VARCHAR(20) NOT NULL,
    message_content TEXT NOT NULL,
    template_id INTEGER,
    delivery_status VARCHAR(20) DEFAULT 'pending' CHECK (delivery_status IN ('pending', 'sent', 'failed', 'delivered', 'expired')),
    delivery_timestamp DATETIME,
    teacher_phone VARCHAR(20),
    teacher_id INTEGER,
    session_id INTEGER,
    attendance_id INTEGER,
    textbee_message_id VARCHAR(100),
    textbee_response TEXT, -- JSON response from TextBee
    error_message TEXT,
    retry_count INTEGER DEFAULT 0,
    cost_estimate DECIMAL(10,4), -- Estimated SMS cost
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (queue_id) REFERENCES sms_queue(queue_id) ON DELETE SET NULL,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (template_id) REFERENCES sms_templates(template_id) ON DELETE SET NULL,
    FOREIGN KEY (teacher_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (session_id) REFERENCES class_sessions(session_id) ON DELETE SET NULL,
    FOREIGN KEY (attendance_id) REFERENCES attendance(attendance_id) ON DELETE SET NULL
);

-- Parent contacts table for managing multiple parent contacts per student
CREATE TABLE IF NOT EXISTS parent_contacts (
    contact_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    contact_name VARCHAR(100) NOT NULL,
    contact_number VARCHAR(20) NOT NULL,
    relationship VARCHAR(50) DEFAULT 'Parent' CHECK (relationship IN ('Parent', 'Guardian', 'Mother', 'Father', 'Grandmother', 'Grandfather', 'Other')),
    is_primary BOOLEAN DEFAULT 0,
    is_emergency BOOLEAN DEFAULT 0,
    receive_attendance BOOLEAN DEFAULT 1,
    receive_announcements BOOLEAN DEFAULT 1,
    is_active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE
);

-- SMS statistics for tracking usage and performance
CREATE TABLE IF NOT EXISTS sms_statistics (
    stat_id INTEGER PRIMARY KEY AUTOINCREMENT,
    date DATE NOT NULL,
    total_sent INTEGER DEFAULT 0,
    total_delivered INTEGER DEFAULT 0,
    total_failed INTEGER DEFAULT 0,
    total_pending INTEGER DEFAULT 0,
    attendance_notifications INTEGER DEFAULT 0,
    absence_notifications INTEGER DEFAULT 0,
    late_notifications INTEGER DEFAULT 0,
    announcement_messages INTEGER DEFAULT 0,
    emergency_messages INTEGER DEFAULT 0,
    total_cost DECIMAL(10,4) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(date)
);

-- Indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_reset_token ON users(reset_token);
CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login);

CREATE INDEX IF NOT EXISTS idx_sessions_expire ON sessions(expire);

CREATE INDEX IF NOT EXISTS idx_students_lrn ON students(lrn);
CREATE INDEX IF NOT EXISTS idx_students_grade_section ON students(grade_level, section);
CREATE INDEX IF NOT EXISTS idx_students_enrollment_status ON students(enrollment_status);
CREATE INDEX IF NOT EXISTS idx_students_qr_code ON students(qr_code_data);

CREATE INDEX IF NOT EXISTS idx_subjects_code ON subjects(subject_code);
CREATE INDEX IF NOT EXISTS idx_subjects_teacher ON subjects(teacher_id);
CREATE INDEX IF NOT EXISTS idx_subjects_grade ON subjects(grade_level);

CREATE INDEX IF NOT EXISTS idx_sessions_datetime ON class_sessions(date_time);
CREATE INDEX IF NOT EXISTS idx_sessions_teacher ON class_sessions(teacher_id);
CREATE INDEX IF NOT EXISTS idx_sessions_subject ON class_sessions(subject_id);
CREATE INDEX IF NOT EXISTS idx_sessions_status ON class_sessions(status);
CREATE INDEX IF NOT EXISTS idx_sessions_qr_code ON class_sessions(qr_code_data);

CREATE INDEX IF NOT EXISTS idx_attendance_student ON attendance(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_session ON attendance(session_id);
CREATE INDEX IF NOT EXISTS idx_attendance_timestamp ON attendance(timestamp);
CREATE INDEX IF NOT EXISTS idx_attendance_status ON attendance(status);
CREATE INDEX IF NOT EXISTS idx_attendance_teacher ON attendance(teacher_id);

-- SMS related indexes
CREATE INDEX IF NOT EXISTS idx_sms_logs_student ON sms_logs(student_id);
CREATE INDEX IF NOT EXISTS idx_sms_logs_created_at ON sms_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_sms_logs_status ON sms_logs(delivery_status);
CREATE INDEX IF NOT EXISTS idx_sms_logs_teacher ON sms_logs(teacher_id);
CREATE INDEX IF NOT EXISTS idx_sms_logs_session ON sms_logs(session_id);
CREATE INDEX IF NOT EXISTS idx_sms_logs_attendance ON sms_logs(attendance_id);

CREATE INDEX IF NOT EXISTS idx_sms_queue_status ON sms_queue(status);
CREATE INDEX IF NOT EXISTS idx_sms_queue_scheduled ON sms_queue(scheduled_at);
CREATE INDEX IF NOT EXISTS idx_sms_queue_priority ON sms_queue(priority);
CREATE INDEX IF NOT EXISTS idx_sms_queue_student ON sms_queue(student_id);
CREATE INDEX IF NOT EXISTS idx_sms_queue_retry ON sms_queue(retry_count);

CREATE INDEX IF NOT EXISTS idx_sms_templates_type ON sms_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_sms_templates_active ON sms_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_parent_contacts_student ON parent_contacts(student_id);
CREATE INDEX IF NOT EXISTS idx_parent_contacts_primary ON parent_contacts(is_primary);
CREATE INDEX IF NOT EXISTS idx_parent_contacts_active ON parent_contacts(is_active);
CREATE INDEX IF NOT EXISTS idx_parent_contacts_number ON parent_contacts(contact_number);

CREATE INDEX IF NOT EXISTS idx_sms_statistics_date ON sms_statistics(date);

-- ============================================================================
-- AI ANALYTICS TABLES
-- ============================================================================

-- AI model metadata and configuration
CREATE TABLE IF NOT EXISTS ai_models (
    model_id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_name VARCHAR(100) NOT NULL UNIQUE,
    model_type VARCHAR(50) NOT NULL CHECK (model_type IN ('time_series', 'risk_classification', 'clustering', 'correlation')),
    model_version VARCHAR(20) NOT NULL,
    model_path VARCHAR(255), -- Path to saved model file
    training_data_hash VARCHAR(64), -- Hash of training data for versioning
    accuracy_score DECIMAL(5,4),
    training_date DATETIME,
    last_used DATETIME,
    is_active BOOLEAN DEFAULT 1,
    hyperparameters TEXT, -- JSON object with model hyperparameters
    feature_names TEXT, -- JSON array of feature names
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Student risk assessments and predictions
CREATE TABLE IF NOT EXISTS student_risk_assessments (
    assessment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    assessment_date DATE NOT NULL,
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high')),
    risk_score DECIMAL(5,4) NOT NULL, -- 0.0 to 1.0
    confidence_score DECIMAL(5,4) NOT NULL, -- 0.0 to 1.0
    model_id INTEGER,
    risk_factors TEXT, -- JSON array of risk factors
    recommendations TEXT, -- JSON array of recommendations
    intervention_status VARCHAR(20) DEFAULT 'none' CHECK (intervention_status IN ('none', 'planned', 'active', 'completed')),
    intervention_notes TEXT,
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(student_id, assessment_date)
);

-- Attendance pattern analysis results
CREATE TABLE IF NOT EXISTS attendance_patterns (
    pattern_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    analysis_date DATE NOT NULL,
    pattern_type VARCHAR(50) NOT NULL CHECK (pattern_type IN ('trend', 'seasonality', 'volatility', 'weekly', 'anomaly')),
    pattern_data TEXT NOT NULL, -- JSON object with pattern details
    strength DECIMAL(5,4), -- Pattern strength (0.0 to 1.0)
    significance DECIMAL(5,4), -- Statistical significance
    model_id INTEGER,
    timeframe_start DATE,
    timeframe_end DATE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL
);

-- Student behavioral clusters
CREATE TABLE IF NOT EXISTS student_clusters (
    cluster_assignment_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    cluster_id INTEGER NOT NULL,
    cluster_label VARCHAR(100) NOT NULL,
    assignment_date DATE NOT NULL,
    confidence_score DECIMAL(5,4) NOT NULL,
    distance_to_centroid DECIMAL(10,6),
    model_id INTEGER,
    cluster_characteristics TEXT, -- JSON object with cluster characteristics
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    UNIQUE(student_id, assignment_date)
);

-- Attendance predictions
CREATE TABLE IF NOT EXISTS attendance_predictions (
    prediction_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    prediction_date DATE NOT NULL,
    target_date DATE NOT NULL, -- Date being predicted
    predicted_status VARCHAR(20) NOT NULL CHECK (predicted_status IN ('present', 'late', 'absent')),
    probability DECIMAL(5,4) NOT NULL, -- Probability of predicted status
    confidence_score DECIMAL(5,4) NOT NULL,
    model_id INTEGER,
    features_used TEXT, -- JSON object with features used for prediction
    actual_status VARCHAR(20) CHECK (actual_status IN ('present', 'late', 'absent')), -- Filled when actual data is available
    prediction_accuracy DECIMAL(5,4), -- Calculated when actual data is available
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL,
    UNIQUE(student_id, prediction_date, target_date)
);

-- Performance correlation analysis
CREATE TABLE IF NOT EXISTS performance_correlations (
    correlation_id INTEGER PRIMARY KEY AUTOINCREMENT,
    analysis_date DATE NOT NULL,
    correlation_type VARCHAR(50) NOT NULL CHECK (correlation_type IN ('overall', 'subject', 'grade_level', 'individual')),
    entity_id INTEGER, -- student_id for individual, subject_id for subject, etc.
    entity_type VARCHAR(20) CHECK (entity_type IN ('student', 'subject', 'grade', 'section')),
    correlation_coefficient DECIMAL(6,4) NOT NULL, -- -1.0 to 1.0
    correlation_strength VARCHAR(20) NOT NULL CHECK (correlation_strength IN ('negligible', 'weak', 'moderate', 'strong')),
    sample_size INTEGER NOT NULL,
    p_value DECIMAL(10,8), -- Statistical significance
    r_squared DECIMAL(5,4), -- Coefficient of determination
    analysis_details TEXT, -- JSON object with detailed analysis
    model_id INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE SET NULL
);

-- Early warning alerts
CREATE TABLE IF NOT EXISTS early_warning_alerts (
    alert_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER NOT NULL,
    alert_type VARCHAR(50) NOT NULL CHECK (alert_type IN ('risk_increase', 'pattern_change', 'prediction_concern', 'intervention_needed')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    alert_message TEXT NOT NULL,
    alert_data TEXT, -- JSON object with alert details
    triggered_by VARCHAR(50), -- What triggered the alert (model, rule, etc.)
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'acknowledged', 'resolved', 'dismissed')),
    acknowledged_by INTEGER,
    acknowledged_at DATETIME,
    resolved_by INTEGER,
    resolved_at DATETIME,
    resolution_notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE,
    FOREIGN KEY (acknowledged_by) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- AI analytics job queue for background processing
CREATE TABLE IF NOT EXISTS ai_job_queue (
    job_id INTEGER PRIMARY KEY AUTOINCREMENT,
    job_type VARCHAR(50) NOT NULL CHECK (job_type IN ('model_training', 'risk_assessment', 'pattern_analysis', 'prediction_update', 'correlation_analysis')),
    job_status VARCHAR(20) DEFAULT 'pending' CHECK (job_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')),
    priority INTEGER DEFAULT 5 CHECK (priority BETWEEN 1 AND 10), -- 1 = highest, 10 = lowest
    job_data TEXT, -- JSON object with job parameters
    progress_percentage INTEGER DEFAULT 0 CHECK (progress_percentage BETWEEN 0 AND 100),
    result_data TEXT, -- JSON object with job results
    error_message TEXT,
    started_at DATETIME,
    completed_at DATETIME,
    estimated_duration INTEGER, -- Estimated duration in seconds
    actual_duration INTEGER, -- Actual duration in seconds
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Model performance metrics and monitoring
CREATE TABLE IF NOT EXISTS model_performance_metrics (
    metric_id INTEGER PRIMARY KEY AUTOINCREMENT,
    model_id INTEGER NOT NULL,
    metric_date DATE NOT NULL,
    metric_type VARCHAR(50) NOT NULL CHECK (metric_type IN ('accuracy', 'precision', 'recall', 'f1_score', 'mse', 'mae', 'r_squared')),
    metric_value DECIMAL(10,6) NOT NULL,
    sample_size INTEGER,
    validation_type VARCHAR(20) CHECK (validation_type IN ('training', 'validation', 'test', 'production')),
    additional_metrics TEXT, -- JSON object with additional metrics
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (model_id) REFERENCES ai_models(model_id) ON DELETE CASCADE,
    UNIQUE(model_id, metric_date, metric_type, validation_type)
);

-- ============================================================================
-- AI ANALYTICS INDEXES
-- ============================================================================

-- AI Models indexes
CREATE INDEX IF NOT EXISTS idx_ai_models_type ON ai_models(model_type);
CREATE INDEX IF NOT EXISTS idx_ai_models_active ON ai_models(is_active);
CREATE INDEX IF NOT EXISTS idx_ai_models_training_date ON ai_models(training_date);
CREATE INDEX IF NOT EXISTS idx_ai_models_last_used ON ai_models(last_used);

-- Student Risk Assessments indexes
CREATE INDEX IF NOT EXISTS idx_risk_assessments_student ON student_risk_assessments(student_id);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_date ON student_risk_assessments(assessment_date);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_level ON student_risk_assessments(risk_level);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_score ON student_risk_assessments(risk_score);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_intervention ON student_risk_assessments(intervention_status);
CREATE INDEX IF NOT EXISTS idx_risk_assessments_model ON student_risk_assessments(model_id);

-- Attendance Patterns indexes
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_student ON attendance_patterns(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_date ON attendance_patterns(analysis_date);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_type ON attendance_patterns(pattern_type);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_timeframe ON attendance_patterns(timeframe_start, timeframe_end);
CREATE INDEX IF NOT EXISTS idx_attendance_patterns_model ON attendance_patterns(model_id);

-- Student Clusters indexes
CREATE INDEX IF NOT EXISTS idx_student_clusters_student ON student_clusters(student_id);
CREATE INDEX IF NOT EXISTS idx_student_clusters_cluster ON student_clusters(cluster_id);
CREATE INDEX IF NOT EXISTS idx_student_clusters_date ON student_clusters(assignment_date);
CREATE INDEX IF NOT EXISTS idx_student_clusters_label ON student_clusters(cluster_label);
CREATE INDEX IF NOT EXISTS idx_student_clusters_model ON student_clusters(model_id);

-- Attendance Predictions indexes
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_student ON attendance_predictions(student_id);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_date ON attendance_predictions(prediction_date);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_target ON attendance_predictions(target_date);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_status ON attendance_predictions(predicted_status);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_model ON attendance_predictions(model_id);
CREATE INDEX IF NOT EXISTS idx_attendance_predictions_accuracy ON attendance_predictions(prediction_accuracy);

-- Performance Correlations indexes
CREATE INDEX IF NOT EXISTS idx_performance_correlations_date ON performance_correlations(analysis_date);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_type ON performance_correlations(correlation_type);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_entity ON performance_correlations(entity_id, entity_type);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_coefficient ON performance_correlations(correlation_coefficient);
CREATE INDEX IF NOT EXISTS idx_performance_correlations_model ON performance_correlations(model_id);

-- Early Warning Alerts indexes
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_student ON early_warning_alerts(student_id);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_type ON early_warning_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_severity ON early_warning_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_status ON early_warning_alerts(status);
CREATE INDEX IF NOT EXISTS idx_early_warning_alerts_created ON early_warning_alerts(created_at);

-- AI Job Queue indexes
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_type ON ai_job_queue(job_type);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_status ON ai_job_queue(job_status);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_priority ON ai_job_queue(priority);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_created ON ai_job_queue(created_at);
CREATE INDEX IF NOT EXISTS idx_ai_job_queue_started ON ai_job_queue(started_at);

-- Model Performance Metrics indexes
CREATE INDEX IF NOT EXISTS idx_model_performance_model ON model_performance_metrics(model_id);
CREATE INDEX IF NOT EXISTS idx_model_performance_date ON model_performance_metrics(metric_date);
CREATE INDEX IF NOT EXISTS idx_model_performance_type ON model_performance_metrics(metric_type);
CREATE INDEX IF NOT EXISTS idx_model_performance_validation ON model_performance_metrics(validation_type);

-- ============================================================================
-- SECURITY AND AUDIT TABLES
-- ============================================================================

-- Enhanced user sessions with security tracking
CREATE TABLE IF NOT EXISTS user_sessions (
    session_id VARCHAR(255) PRIMARY KEY,
    user_id INTEGER NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT 1,
    device_fingerprint VARCHAR(255),
    location_data TEXT, -- JSON with location info
    login_method VARCHAR(20) DEFAULT 'password' CHECK (login_method IN ('password', '2fa', 'backup_code')),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Comprehensive audit logging
CREATE TABLE IF NOT EXISTS audit_logs (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50),
    resource_id VARCHAR(50),
    old_values TEXT, -- JSON
    new_values TEXT, -- JSON
    ip_address VARCHAR(45),
    user_agent TEXT,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    severity VARCHAR(20) DEFAULT 'info' CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    category VARCHAR(50) DEFAULT 'general',
    session_id VARCHAR(255),
    request_id VARCHAR(100),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Security events and threat monitoring
CREATE TABLE IF NOT EXISTS security_events (
    event_id INTEGER PRIMARY KEY AUTOINCREMENT,
    event_type VARCHAR(50) NOT NULL,
    user_id INTEGER,
    ip_address VARCHAR(45),
    user_agent TEXT,
    event_data TEXT, -- JSON
    risk_level VARCHAR(20) DEFAULT 'low' CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    resolved BOOLEAN DEFAULT 0,
    resolved_by INTEGER,
    resolved_at DATETIME,
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    auto_blocked BOOLEAN DEFAULT 0,
    block_duration INTEGER, -- minutes
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE SET NULL,
    FOREIGN KEY (resolved_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- Granular permissions system
CREATE TABLE IF NOT EXISTS permissions (
    permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
    permission_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    resource_type VARCHAR(50),
    action_type VARCHAR(50),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Role-based permission assignments
CREATE TABLE IF NOT EXISTS role_permissions (
    role_permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
    role VARCHAR(20) NOT NULL,
    permission_id INTEGER NOT NULL,
    granted BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    UNIQUE(role, permission_id)
);

-- User-specific permission overrides
CREATE TABLE IF NOT EXISTS user_permissions (
    user_permission_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    permission_id INTEGER NOT NULL,
    granted BOOLEAN DEFAULT 1,
    granted_by INTEGER,
    granted_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    reason TEXT,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    FOREIGN KEY (permission_id) REFERENCES permissions(permission_id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(user_id) ON DELETE SET NULL,
    UNIQUE(user_id, permission_id)
);

-- Data access logging for compliance
CREATE TABLE IF NOT EXISTS data_access_logs (
    access_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    resource_type VARCHAR(50) NOT NULL,
    resource_id VARCHAR(50),
    access_type VARCHAR(20) NOT NULL, -- read, write, delete, export
    data_classification VARCHAR(20) DEFAULT 'public', -- public, internal, confidential, restricted
    purpose VARCHAR(100),
    ip_address VARCHAR(45),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    session_id VARCHAR(255),
    query_details TEXT, -- JSON with query/filter details
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Privacy consent management
CREATE TABLE IF NOT EXISTS privacy_consents (
    consent_id INTEGER PRIMARY KEY AUTOINCREMENT,
    student_id INTEGER,
    parent_name VARCHAR(100),
    parent_contact VARCHAR(20),
    parent_email VARCHAR(100),
    consent_type VARCHAR(50) NOT NULL, -- photo, data_processing, communication, analytics
    consent_given BOOLEAN NOT NULL,
    consent_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    consent_withdrawn_date DATETIME,
    legal_basis VARCHAR(100),
    data_retention_period INTEGER, -- days
    notes TEXT,
    digital_signature TEXT,
    ip_address VARCHAR(45),
    FOREIGN KEY (student_id) REFERENCES students(student_id) ON DELETE CASCADE
);

-- Data retention and deletion policies
CREATE TABLE IF NOT EXISTS data_retention_policies (
    policy_id INTEGER PRIMARY KEY AUTOINCREMENT,
    data_type VARCHAR(50) NOT NULL,
    retention_period_days INTEGER NOT NULL,
    auto_delete BOOLEAN DEFAULT 0,
    legal_basis VARCHAR(100),
    deletion_method VARCHAR(50) DEFAULT 'soft_delete',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(data_type)
);

-- Encrypted sensitive data storage
CREATE TABLE IF NOT EXISTS encrypted_data (
    data_id INTEGER PRIMARY KEY AUTOINCREMENT,
    table_name VARCHAR(50) NOT NULL,
    record_id INTEGER NOT NULL,
    field_name VARCHAR(50) NOT NULL,
    encrypted_value TEXT NOT NULL,
    encryption_method VARCHAR(50) DEFAULT 'AES-256-GCM',
    key_version INTEGER DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(table_name, record_id, field_name)
);

-- Password history for policy enforcement
CREATE TABLE IF NOT EXISTS password_history (
    history_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- Login attempts tracking
CREATE TABLE IF NOT EXISTS login_attempts (
    attempt_id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50),
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    failure_reason VARCHAR(100),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    blocked BOOLEAN DEFAULT 0
);

-- Security alerts and notifications
CREATE TABLE IF NOT EXISTS security_alerts (
    alert_id INTEGER PRIMARY KEY AUTOINCREMENT,
    alert_type VARCHAR(50) NOT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    severity VARCHAR(20) DEFAULT 'medium' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    target_users TEXT, -- JSON array of user IDs or roles
    read_by TEXT, -- JSON array of user IDs who read the alert
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    expires_at DATETIME,
    is_active BOOLEAN DEFAULT 1,
    FOREIGN KEY (created_by) REFERENCES users(user_id) ON DELETE SET NULL
);

-- File upload security tracking
CREATE TABLE IF NOT EXISTS file_security_logs (
    log_id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id INTEGER NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INTEGER NOT NULL,
    mime_type VARCHAR(100) NOT NULL,
    file_hash VARCHAR(64) NOT NULL,
    scan_status VARCHAR(20) DEFAULT 'pending' CHECK (scan_status IN ('pending', 'clean', 'infected', 'suspicious')),
    scan_details TEXT, -- JSON with scan results
    upload_ip VARCHAR(45),
    timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE
);

-- ============================================================================
-- SECURITY INDEXES
-- ============================================================================

-- User sessions indexes
CREATE INDEX IF NOT EXISTS idx_user_sessions_user_id ON user_sessions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_sessions_expires ON user_sessions(expires_at);
CREATE INDEX IF NOT EXISTS idx_user_sessions_active ON user_sessions(is_active);
CREATE INDEX IF NOT EXISTS idx_user_sessions_ip ON user_sessions(ip_address);

-- Audit logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_timestamp ON audit_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_severity ON audit_logs(severity);
CREATE INDEX IF NOT EXISTS idx_audit_logs_category ON audit_logs(category);

-- Security events indexes
CREATE INDEX IF NOT EXISTS idx_security_events_type ON security_events(event_type);
CREATE INDEX IF NOT EXISTS idx_security_events_timestamp ON security_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_security_events_risk ON security_events(risk_level);
CREATE INDEX IF NOT EXISTS idx_security_events_resolved ON security_events(resolved);
CREATE INDEX IF NOT EXISTS idx_security_events_ip ON security_events(ip_address);

-- Permissions indexes
CREATE INDEX IF NOT EXISTS idx_permissions_name ON permissions(permission_name);
CREATE INDEX IF NOT EXISTS idx_permissions_category ON permissions(category);
CREATE INDEX IF NOT EXISTS idx_role_permissions_role ON role_permissions(role);
CREATE INDEX IF NOT EXISTS idx_user_permissions_user ON user_permissions(user_id);
CREATE INDEX IF NOT EXISTS idx_user_permissions_expires ON user_permissions(expires_at);

-- Data access logs indexes
CREATE INDEX IF NOT EXISTS idx_data_access_logs_user ON data_access_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_timestamp ON data_access_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_resource ON data_access_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_data_access_logs_classification ON data_access_logs(data_classification);

-- Privacy consents indexes
CREATE INDEX IF NOT EXISTS idx_privacy_consents_student ON privacy_consents(student_id);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_type ON privacy_consents(consent_type);
CREATE INDEX IF NOT EXISTS idx_privacy_consents_given ON privacy_consents(consent_given);

-- Encrypted data indexes
CREATE INDEX IF NOT EXISTS idx_encrypted_data_table_record ON encrypted_data(table_name, record_id);
CREATE INDEX IF NOT EXISTS idx_encrypted_data_field ON encrypted_data(field_name);

-- Password history indexes
CREATE INDEX IF NOT EXISTS idx_password_history_user ON password_history(user_id);
CREATE INDEX IF NOT EXISTS idx_password_history_created ON password_history(created_at);

-- Login attempts indexes
CREATE INDEX IF NOT EXISTS idx_login_attempts_username ON login_attempts(username);
CREATE INDEX IF NOT EXISTS idx_login_attempts_ip ON login_attempts(ip_address);
CREATE INDEX IF NOT EXISTS idx_login_attempts_timestamp ON login_attempts(timestamp);
CREATE INDEX IF NOT EXISTS idx_login_attempts_success ON login_attempts(success);

-- Security alerts indexes
CREATE INDEX IF NOT EXISTS idx_security_alerts_type ON security_alerts(alert_type);
CREATE INDEX IF NOT EXISTS idx_security_alerts_severity ON security_alerts(severity);
CREATE INDEX IF NOT EXISTS idx_security_alerts_active ON security_alerts(is_active);
CREATE INDEX IF NOT EXISTS idx_security_alerts_created ON security_alerts(created_at);

-- File security logs indexes
CREATE INDEX IF NOT EXISTS idx_file_security_logs_user ON file_security_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_file_security_logs_timestamp ON file_security_logs(timestamp);
CREATE INDEX IF NOT EXISTS idx_file_security_logs_scan_status ON file_security_logs(scan_status);
CREATE INDEX IF NOT EXISTS idx_file_security_logs_hash ON file_security_logs(file_hash);
