{"name": "express-brute", "version": "1.0.1", "description": "A brute-force protection middleware for express routes that rate limits incoming requests", "keywords": ["brute", "force", "bruteforce", "attack", "<PERSON><PERSON><PERSON><PERSON>", "rate", "limit", "security"], "license": "BSD", "private": false, "scripts": {"test": "./node_modules/.bin/istanbul cover ./node_modules/mocha/bin/_mocha spec"}, "repository": {"type": "git", "url": "**************:AdamPflug/express-brute.git"}, "devDependencies": {"chai": "~3.5.0", "coveralls": "~2.11.9", "istanbul": "~0.4.3", "mocha": "~2.4.5", "mocha-lcov-reporter": "~1.2.0", "sinon": "~1.17.3", "sinon-chai": "~2.8.0"}, "dependencies": {"long-timeout": "~0.1.1", "underscore": "~1.8.3"}, "peerDependencies": {"express": "4.x"}}