const crypto = require('crypto');
const fs = require('fs');
const path = require('path');
const securityConfig = require('../config/security-config');

/**
 * Encryption Service
 * Handles data encryption, decryption, and key management
 */
class EncryptionService {
    constructor() {
        this.config = securityConfig.get('encryption');
        this.algorithm = this.config.algorithm;
        this.keyPath = path.join(__dirname, '../config/encryption-keys.json');
        this.keys = this.loadKeys();
        this.currentKeyVersion = this.getCurrentKeyVersion();
    }

    /**
     * Load encryption keys from file
     * @returns {Object} Encryption keys
     */
    loadKeys() {
        try {
            if (fs.existsSync(this.keyPath)) {
                const keyData = fs.readFileSync(this.keyPath, 'utf8');
                return JSON.parse(keyData);
            } else {
                // Generate initial key
                const initialKey = this.generateKey();
                const keys = {
                    version: 1,
                    keys: {
                        1: {
                            key: initialKey,
                            created: new Date().toISOString(),
                            active: true
                        }
                    }
                };
                this.saveKeys(keys);
                return keys;
            }
        } catch (error) {
            console.error('Error loading encryption keys:', error);
            throw new Error('Failed to load encryption keys');
        }
    }

    /**
     * Save encryption keys to file
     * @param {Object} keys - Keys object
     */
    saveKeys(keys) {
        try {
            fs.writeFileSync(this.keyPath, JSON.stringify(keys, null, 2), { mode: 0o600 });
        } catch (error) {
            console.error('Error saving encryption keys:', error);
            throw new Error('Failed to save encryption keys');
        }
    }

    /**
     * Generate new encryption key
     * @returns {string} Base64 encoded key
     */
    generateKey() {
        return crypto.randomBytes(32).toString('base64');
    }

    /**
     * Get current key version
     * @returns {number} Current key version
     */
    getCurrentKeyVersion() {
        return this.keys.version || 1;
    }

    /**
     * Get encryption key by version
     * @param {number} version - Key version
     * @returns {string} Encryption key
     */
    getKey(version = null) {
        const keyVersion = version || this.currentKeyVersion;
        const keyData = this.keys.keys[keyVersion];
        
        if (!keyData) {
            throw new Error(`Encryption key version ${keyVersion} not found`);
        }
        
        return keyData.key;
    }

    /**
     * Encrypt data
     * @param {string} plaintext - Data to encrypt
     * @param {number} keyVersion - Key version to use (optional)
     * @returns {Promise<string>} Encrypted data with metadata
     */
    async encrypt(plaintext, keyVersion = null) {
        try {
            if (!plaintext) {
                return null;
            }

            const version = keyVersion || this.currentKeyVersion;
            const key = Buffer.from(this.getKey(version), 'base64');
            const iv = crypto.randomBytes(16);
            
            const cipher = crypto.createCipher(this.algorithm, key);
            cipher.setAutoPadding(true);
            
            let encrypted = cipher.update(plaintext, 'utf8', 'hex');
            encrypted += cipher.final('hex');
            
            const authTag = cipher.getAuthTag ? cipher.getAuthTag().toString('hex') : '';
            
            // Create encrypted data object with metadata
            const encryptedData = {
                version: version,
                algorithm: this.algorithm,
                iv: iv.toString('hex'),
                data: encrypted,
                authTag: authTag
            };
            
            return Buffer.from(JSON.stringify(encryptedData)).toString('base64');
            
        } catch (error) {
            console.error('Encryption error:', error);
            throw new Error('Encryption failed');
        }
    }

    /**
     * Decrypt data
     * @param {string} encryptedData - Encrypted data with metadata
     * @returns {Promise<string>} Decrypted plaintext
     */
    async decrypt(encryptedData) {
        try {
            if (!encryptedData) {
                return null;
            }

            // Parse encrypted data object
            const dataObj = JSON.parse(Buffer.from(encryptedData, 'base64').toString('utf8'));
            const { version, algorithm, iv, data, authTag } = dataObj;
            
            const key = Buffer.from(this.getKey(version), 'base64');
            const ivBuffer = Buffer.from(iv, 'hex');
            
            const decipher = crypto.createDecipher(algorithm, key);
            
            if (authTag && decipher.setAuthTag) {
                decipher.setAuthTag(Buffer.from(authTag, 'hex'));
            }
            
            let decrypted = decipher.update(data, 'hex', 'utf8');
            decrypted += decipher.final('utf8');
            
            return decrypted;
            
        } catch (error) {
            console.error('Decryption error:', error);
            throw new Error('Decryption failed');
        }
    }

    /**
     * Hash data using SHA-256
     * @param {string} data - Data to hash
     * @param {string} salt - Salt (optional)
     * @returns {string} Hash
     */
    hash(data, salt = '') {
        const hash = crypto.createHash('sha256');
        hash.update(data + salt);
        return hash.digest('hex');
    }

    /**
     * Generate secure random salt
     * @param {number} length - Salt length in bytes
     * @returns {string} Random salt
     */
    generateSalt(length = 16) {
        return crypto.randomBytes(length).toString('hex');
    }

    /**
     * Encrypt sensitive field in database record
     * @param {string} tableName - Table name
     * @param {number} recordId - Record ID
     * @param {string} fieldName - Field name
     * @param {string} value - Value to encrypt
     * @returns {Promise<boolean>} Success status
     */
    async encryptField(tableName, recordId, fieldName, value) {
        try {
            if (!this.config.encryptSensitiveFields || !this.config.sensitiveFields.includes(fieldName)) {
                return false;
            }

            const encryptedValue = await this.encrypt(value);
            
            // Store in encrypted_data table
            const dbConnection = require('../database/connection');
            const db = dbConnection.getDatabase();
            
            await new Promise((resolve, reject) => {
                db.run(
                    `INSERT OR REPLACE INTO encrypted_data 
                     (table_name, record_id, field_name, encrypted_value, key_version)
                     VALUES (?, ?, ?, ?, ?)`,
                    [tableName, recordId, fieldName, encryptedValue, this.currentKeyVersion],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            return true;
        } catch (error) {
            console.error('Field encryption error:', error);
            return false;
        }
    }

    /**
     * Decrypt sensitive field from database
     * @param {string} tableName - Table name
     * @param {number} recordId - Record ID
     * @param {string} fieldName - Field name
     * @returns {Promise<string|null>} Decrypted value
     */
    async decryptField(tableName, recordId, fieldName) {
        try {
            const dbConnection = require('../database/connection');
            const db = dbConnection.getDatabase();
            
            const result = await new Promise((resolve, reject) => {
                db.get(
                    `SELECT encrypted_value FROM encrypted_data 
                     WHERE table_name = ? AND record_id = ? AND field_name = ?`,
                    [tableName, recordId, fieldName],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });

            if (!result) {
                return null;
            }

            return await this.decrypt(result.encrypted_value);
        } catch (error) {
            console.error('Field decryption error:', error);
            return null;
        }
    }

    /**
     * Rotate encryption keys
     * @returns {Promise<boolean>} Success status
     */
    async rotateKeys() {
        try {
            const newVersion = this.currentKeyVersion + 1;
            const newKey = this.generateKey();
            
            // Add new key
            this.keys.keys[newVersion] = {
                key: newKey,
                created: new Date().toISOString(),
                active: true
            };
            
            // Mark old key as inactive
            if (this.keys.keys[this.currentKeyVersion]) {
                this.keys.keys[this.currentKeyVersion].active = false;
            }
            
            // Update version
            this.keys.version = newVersion;
            this.currentKeyVersion = newVersion;
            
            // Save keys
            this.saveKeys(this.keys);
            
            console.log(`Encryption key rotated to version ${newVersion}`);
            return true;
            
        } catch (error) {
            console.error('Key rotation error:', error);
            return false;
        }
    }

    /**
     * Re-encrypt data with new key version
     * @param {string} encryptedData - Data encrypted with old key
     * @param {number} newKeyVersion - New key version
     * @returns {Promise<string>} Re-encrypted data
     */
    async reEncrypt(encryptedData, newKeyVersion = null) {
        try {
            const plaintext = await this.decrypt(encryptedData);
            return await this.encrypt(plaintext, newKeyVersion || this.currentKeyVersion);
        } catch (error) {
            console.error('Re-encryption error:', error);
            throw error;
        }
    }

    /**
     * Anonymize sensitive data
     * @param {string} data - Data to anonymize
     * @param {string} type - Data type (email, phone, name, etc.)
     * @returns {string} Anonymized data
     */
    anonymize(data, type = 'general') {
        if (!data) return data;

        switch (type) {
            case 'email':
                const emailParts = data.split('@');
                if (emailParts.length === 2) {
                    const username = emailParts[0];
                    const domain = emailParts[1];
                    const maskedUsername = username.length > 2 
                        ? username.substring(0, 2) + '*'.repeat(username.length - 2)
                        : '*'.repeat(username.length);
                    return `${maskedUsername}@${domain}`;
                }
                break;
                
            case 'phone':
                if (data.length > 4) {
                    return '*'.repeat(data.length - 4) + data.slice(-4);
                }
                break;
                
            case 'name':
                const nameParts = data.split(' ');
                return nameParts.map(part => 
                    part.length > 1 ? part.charAt(0) + '*'.repeat(part.length - 1) : part
                ).join(' ');
                
            default:
                // General anonymization - keep first and last character
                if (data.length > 2) {
                    return data.charAt(0) + '*'.repeat(data.length - 2) + data.charAt(data.length - 1);
                } else {
                    return '*'.repeat(data.length);
                }
        }
        
        return data;
    }

    /**
     * Generate pseudonym for data
     * @param {string} data - Original data
     * @param {string} salt - Salt for consistency
     * @returns {string} Pseudonym
     */
    generatePseudonym(data, salt = '') {
        const hash = this.hash(data, salt);
        return `ANON_${hash.substring(0, 8).toUpperCase()}`;
    }

    /**
     * Check if key rotation is needed
     * @returns {boolean} True if rotation is needed
     */
    isKeyRotationNeeded() {
        const currentKey = this.keys.keys[this.currentKeyVersion];
        if (!currentKey) return true;
        
        const keyAge = Date.now() - new Date(currentKey.created).getTime();
        const maxAge = this.config.keyRotationDays * 24 * 60 * 60 * 1000;
        
        return keyAge > maxAge;
    }
}

module.exports = new EncryptionService();
