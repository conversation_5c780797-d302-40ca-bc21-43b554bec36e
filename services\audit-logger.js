const winston = require('winston');
const DailyRotateFile = require('winston-daily-rotate-file');
const path = require('path');
const fs = require('fs');
const dbConnection = require('../database/connection');
const securityConfig = require('../config/security-config');

/**
 * Audit Logger Service
 * Comprehensive logging and audit trail system
 */
class AuditLogger {
    constructor() {
        this.config = securityConfig.get('audit');
        this.db = null;
        this.setupLogger();
        this.setupDatabaseLogging();
    }

    // Initialize database connection
    initializeDatabase() {
        if (!this.db) {
            this.db = dbConnection.getDatabase();
        }
        return this.db;
    }

    /**
     * Setup Winston logger with file rotation
     */
    setupLogger() {
        const logDir = path.join(__dirname, '../logs');
        
        // Ensure log directory exists
        if (!fs.existsSync(logDir)) {
            fs.mkdirSync(logDir, { recursive: true });
        }

        // Create logger with multiple transports
        this.logger = winston.createLogger({
            level: this.config.logLevel,
            format: winston.format.combine(
                winston.format.timestamp(),
                winston.format.errors({ stack: true }),
                winston.format.json()
            ),
            defaultMeta: { service: 'qrsams-audit' },
            transports: [
                // Console transport for development
                new winston.transports.Console({
                    format: winston.format.combine(
                        winston.format.colorize(),
                        winston.format.simple()
                    )
                }),

                // Daily rotate file for general logs
                new DailyRotateFile({
                    filename: path.join(logDir, 'audit-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: `${this.config.retentionDays}d`,
                    level: 'info'
                }),

                // Separate file for security events
                new DailyRotateFile({
                    filename: path.join(logDir, 'security-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: `${this.config.retentionDays}d`,
                    level: 'warn',
                    format: winston.format.combine(
                        winston.format.timestamp(),
                        winston.format.json(),
                        winston.format.printf(info => {
                            return JSON.stringify({
                                timestamp: info.timestamp,
                                level: info.level,
                                message: info.message,
                                ...info
                            });
                        })
                    )
                }),

                // Error logs
                new DailyRotateFile({
                    filename: path.join(logDir, 'error-%DATE%.log'),
                    datePattern: 'YYYY-MM-DD',
                    zippedArchive: true,
                    maxSize: '20m',
                    maxFiles: `${this.config.retentionDays}d`,
                    level: 'error'
                })
            ]
        });
    }

    /**
     * Setup database logging tables and cleanup
     */
    setupDatabaseLogging() {
        // Setup periodic cleanup of old audit logs
        setInterval(() => {
            this.cleanupOldLogs();
        }, 24 * 60 * 60 * 1000); // Daily cleanup
    }

    /**
     * Log audit event to database and file
     * @param {number} userId - User ID
     * @param {string} action - Action performed
     * @param {string} resourceType - Type of resource
     * @param {string} resourceId - Resource ID
     * @param {Object} oldValues - Old values (for updates)
     * @param {Object} newValues - New values (for updates)
     * @param {string} ip - IP address
     * @param {string} userAgent - User agent
     * @param {string} sessionId - Session ID
     * @param {string} severity - Log severity
     * @param {string} category - Log category
     */
    async log(userId, action, resourceType, resourceId, oldValues = null, newValues = null, 
              ip = null, userAgent = null, sessionId = null, severity = 'info', category = 'general') {
        
        try {
            const logData = {
                userId,
                action,
                resourceType,
                resourceId,
                oldValues: oldValues ? JSON.stringify(oldValues) : null,
                newValues: newValues ? JSON.stringify(newValues) : null,
                ip,
                userAgent,
                sessionId,
                severity,
                category,
                timestamp: new Date().toISOString()
            };

            // Log to database
            if (this.config.enabled) {
                await this.logToDatabase(logData);
            }

            // Log to file
            this.logToFile(logData);

            // Check for real-time alerts
            if (this.config.realTimeAlerts) {
                await this.checkAlertConditions(logData);
            }

        } catch (error) {
            console.error('Audit logging error:', error);
            // Fallback to file logging only
            this.logger.error('Audit logging failed', { error: error.message, userId, action });
        }
    }

    /**
     * Log to database
     * @param {Object} logData - Log data
     */
    async logToDatabase(logData) {
        this.initializeDatabase();
        return new Promise((resolve, reject) => {
            this.db.run(
                `INSERT INTO audit_logs 
                 (user_id, action, resource_type, resource_id, old_values, new_values, 
                  ip_address, user_agent, session_id, severity, category, timestamp)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                    logData.userId, logData.action, logData.resourceType, logData.resourceId,
                    logData.oldValues, logData.newValues, logData.ip, logData.userAgent,
                    logData.sessionId, logData.severity, logData.category, logData.timestamp
                ],
                function(err) {
                    if (err) reject(err);
                    else resolve(this.lastID);
                }
            );
        });
    }

    /**
     * Log to file
     * @param {Object} logData - Log data
     */
    logToFile(logData) {
        const logLevel = this.mapSeverityToLevel(logData.severity);
        
        const logMessage = {
            action: logData.action,
            resourceType: logData.resourceType,
            resourceId: logData.resourceId,
            userId: logData.userId,
            ip: logData.ip,
            category: logData.category
        };

        // Add sensitive data only if configured
        if (this.config.logSensitiveData) {
            logMessage.oldValues = logData.oldValues;
            logMessage.newValues = logData.newValues;
            logMessage.userAgent = logData.userAgent;
        }

        this.logger.log(logLevel, `${logData.action} on ${logData.resourceType}`, logMessage);
    }

    /**
     * Log security event
     * @param {string} eventType - Event type
     * @param {number} userId - User ID
     * @param {string} ip - IP address
     * @param {Object} eventData - Event data
     * @param {string} riskLevel - Risk level
     * @param {string} userAgent - User agent
     */
    async logSecurityEvent(eventType, userId, ip, eventData, riskLevel = 'low', userAgent = null) {
        try {
            // Log to database
            await new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT INTO security_events 
                     (event_type, user_id, ip_address, user_agent, event_data, risk_level, timestamp)
                     VALUES (?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                    [eventType, userId, ip, userAgent, JSON.stringify(eventData), riskLevel],
                    function(err) {
                        if (err) reject(err);
                        else resolve(this.lastID);
                    }
                );
            });

            // Log to file with appropriate severity
            const severity = this.mapRiskToSeverity(riskLevel);
            this.logger.log(severity, `Security event: ${eventType}`, {
                eventType,
                userId,
                ip,
                riskLevel,
                eventData: this.config.logSensitiveData ? eventData : '[REDACTED]'
            });

            // Check for immediate alerts
            if (riskLevel === 'high' || riskLevel === 'critical') {
                await this.triggerSecurityAlert(eventType, userId, ip, eventData, riskLevel);
            }

        } catch (error) {
            console.error('Security event logging error:', error);
        }
    }

    /**
     * Log data access
     * @param {number} userId - User ID
     * @param {string} resourceType - Resource type
     * @param {string} resourceId - Resource ID
     * @param {string} accessType - Access type (read, write, delete, export)
     * @param {string} dataClassification - Data classification
     * @param {string} purpose - Access purpose
     * @param {string} ip - IP address
     * @param {string} sessionId - Session ID
     * @param {Object} queryDetails - Query details
     */
    async logDataAccess(userId, resourceType, resourceId, accessType, dataClassification = 'public', 
                       purpose = null, ip = null, sessionId = null, queryDetails = null) {
        try {
            await new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT INTO data_access_logs 
                     (user_id, resource_type, resource_id, access_type, data_classification, 
                      purpose, ip_address, session_id, query_details, timestamp)
                     VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                    [
                        userId, resourceType, resourceId, accessType, dataClassification,
                        purpose, ip, sessionId, queryDetails ? JSON.stringify(queryDetails) : null
                    ],
                    function(err) {
                        if (err) reject(err);
                        else resolve(this.lastID);
                    }
                );
            });

            // Log to audit trail
            await this.log(
                userId, 
                `data_${accessType}`, 
                resourceType, 
                resourceId, 
                null, 
                { dataClassification, purpose },
                ip, 
                null, 
                sessionId, 
                dataClassification === 'restricted' ? 'warn' : 'info',
                'data_access'
            );

        } catch (error) {
            console.error('Data access logging error:', error);
        }
    }

    /**
     * Get audit logs with filtering
     * @param {Object} filters - Filter criteria
     * @param {number} limit - Limit results
     * @param {number} offset - Offset for pagination
     * @returns {Promise<Array>} Audit logs
     */
    async getAuditLogs(filters = {}, limit = 100, offset = 0) {
        try {
            let query = `
                SELECT al.*, u.username, u.full_name 
                FROM audit_logs al
                LEFT JOIN users u ON al.user_id = u.user_id
                WHERE 1=1
            `;
            const params = [];

            // Apply filters
            if (filters.userId) {
                query += ' AND al.user_id = ?';
                params.push(filters.userId);
            }

            if (filters.action) {
                query += ' AND al.action LIKE ?';
                params.push(`%${filters.action}%`);
            }

            if (filters.resourceType) {
                query += ' AND al.resource_type = ?';
                params.push(filters.resourceType);
            }

            if (filters.severity) {
                query += ' AND al.severity = ?';
                params.push(filters.severity);
            }

            if (filters.category) {
                query += ' AND al.category = ?';
                params.push(filters.category);
            }

            if (filters.startDate) {
                query += ' AND al.timestamp >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                query += ' AND al.timestamp <= ?';
                params.push(filters.endDate);
            }

            if (filters.ip) {
                query += ' AND al.ip_address = ?';
                params.push(filters.ip);
            }

            query += ' ORDER BY al.timestamp DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            return new Promise((resolve, reject) => {
                this.db.all(query, params, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

        } catch (error) {
            console.error('Get audit logs error:', error);
            return [];
        }
    }

    /**
     * Get security events
     * @param {Object} filters - Filter criteria
     * @param {number} limit - Limit results
     * @param {number} offset - Offset for pagination
     * @returns {Promise<Array>} Security events
     */
    async getSecurityEvents(filters = {}, limit = 100, offset = 0) {
        try {
            let query = `
                SELECT se.*, u.username, u.full_name 
                FROM security_events se
                LEFT JOIN users u ON se.user_id = u.user_id
                WHERE 1=1
            `;
            const params = [];

            // Apply filters
            if (filters.eventType) {
                query += ' AND se.event_type = ?';
                params.push(filters.eventType);
            }

            if (filters.riskLevel) {
                query += ' AND se.risk_level = ?';
                params.push(filters.riskLevel);
            }

            if (filters.resolved !== undefined) {
                query += ' AND se.resolved = ?';
                params.push(filters.resolved ? 1 : 0);
            }

            if (filters.startDate) {
                query += ' AND se.timestamp >= ?';
                params.push(filters.startDate);
            }

            if (filters.endDate) {
                query += ' AND se.timestamp <= ?';
                params.push(filters.endDate);
            }

            query += ' ORDER BY se.timestamp DESC LIMIT ? OFFSET ?';
            params.push(limit, offset);

            return new Promise((resolve, reject) => {
                this.db.all(query, params, (err, rows) => {
                    if (err) reject(err);
                    else resolve(rows);
                });
            });

        } catch (error) {
            console.error('Get security events error:', error);
            return [];
        }
    }

    /**
     * Check alert conditions and trigger if necessary
     * @param {Object} logData - Log data
     */
    async checkAlertConditions(logData) {
        try {
            const monitoring = securityConfig.get('monitoring');
            if (!monitoring.enabled) return;

            const thresholds = monitoring.alertThresholds;
            const now = new Date();
            const oneHourAgo = new Date(now.getTime() - 60 * 60 * 1000);

            // Check failed login threshold
            if (logData.action === 'failed_login') {
                const recentFailures = await this.getRecentEventCount('failed_login', oneHourAgo);
                if (recentFailures >= thresholds.failedLogins) {
                    await this.createAlert('high_failed_logins', 'High number of failed login attempts detected', 'high');
                }
            }

            // Check data access threshold
            if (logData.category === 'data_access') {
                const recentAccess = await this.getRecentEventCount('data_access', oneHourAgo);
                if (recentAccess >= thresholds.dataAccess) {
                    await this.createAlert('high_data_access', 'Unusual data access pattern detected', 'medium');
                }
            }

        } catch (error) {
            console.error('Alert condition check error:', error);
        }
    }

    /**
     * Helper methods
     */
    mapSeverityToLevel(severity) {
        const mapping = {
            'info': 'info',
            'warning': 'warn',
            'error': 'error',
            'critical': 'error'
        };
        return mapping[severity] || 'info';
    }

    mapRiskToSeverity(riskLevel) {
        const mapping = {
            'low': 'info',
            'medium': 'warn',
            'high': 'error',
            'critical': 'error'
        };
        return mapping[riskLevel] || 'info';
    }

    async cleanupOldLogs() {
        try {
            const cutoffDate = new Date(Date.now() - this.config.retentionDays * 24 * 60 * 60 * 1000);
            
            await new Promise((resolve, reject) => {
                this.db.run(
                    'DELETE FROM audit_logs WHERE timestamp < ?',
                    [cutoffDate.toISOString()],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            this.logger.info('Old audit logs cleaned up', { cutoffDate });
        } catch (error) {
            console.error('Log cleanup error:', error);
        }
    }

    async getRecentEventCount(eventType, since) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT COUNT(*) as count FROM audit_logs WHERE action = ? AND timestamp > ?',
                [eventType, since.toISOString()],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row.count || 0);
                }
            );
        });
    }

    async createAlert(type, message, severity) {
        try {
            await new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT INTO security_alerts 
                     (alert_type, title, message, severity, created_at)
                     VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)`,
                    [type, `Security Alert: ${type}`, message, severity],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });
        } catch (error) {
            console.error('Alert creation error:', error);
        }
    }

    async triggerSecurityAlert(eventType, userId, ip, eventData, riskLevel) {
        // Implementation for immediate security alerts (email, dashboard notifications, etc.)
        this.logger.error('SECURITY ALERT', {
            eventType,
            userId,
            ip,
            riskLevel,
            eventData,
            timestamp: new Date().toISOString()
        });
    }
}

module.exports = new AuditLogger();
