const express = require('express');
const cors = require('cors');
const bodyParser = require('body-parser');
const path = require('path');
const multer = require('multer');
const moment = require('moment');
const session = require('express-session');
const SQLiteStore = require('connect-sqlite3')(session);
const dbConnection = require('./database/connection');
const NetworkConfig = require('./config/network-config');
const securityConfig = require('./config/security-config');

// Import security middleware
const {
    createRateLimiter,
    createSlowDown,
    bruteForceProtection,
    securityHeaders,
    xssProtection,
    hppProtection,
    csrfProtection,
    requireAuth,
    requirePermission,
    requireRole,
    validateInput,
    secureFileUpload,
    securityMonitoring
} = require('./middleware/security');

// Import services
const auditLogger = require('./services/audit-logger');
const encryptionService = require('./services/encryption-service');
const permissionService = require('./services/permission-service');

// Initialize network configuration
const networkConfig = new NetworkConfig();
const serverConfig = networkConfig.getServerConfig();

// Initialize Express app
const app = express();
const PORT = serverConfig.port;

// View engine setup
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));

// Security middleware - Apply early in the middleware stack
app.use(securityHeaders);
app.use(xssProtection);
app.use(hppProtection);
app.use(securityMonitoring);

// Rate limiting middleware
app.use(createRateLimiter());
app.use(createSlowDown());

// Session configuration with enhanced security
const sessionConfig = securityConfig.get('session');
app.use(session({
    store: new SQLiteStore({
        db: 'sessions.db',
        dir: './database'
    }),
    secret: process.env.SESSION_SECRET || 'qrsams-secret-key-change-in-production',
    resave: false,
    saveUninitialized: false,
    name: sessionConfig.name || 'qrsams.sid',
    cookie: {
        secure: process.env.NODE_ENV === 'production', // HTTPS only in production
        httpOnly: true,
        maxAge: sessionConfig.maxAge || 24 * 60 * 60 * 1000, // 24 hours
        sameSite: 'strict'
    },
    rolling: true // Reset expiration on activity
}));

// CSRF protection (after session)
app.use('/api', csrfProtection);

// Middleware configuration with dynamic CORS for local network
app.use(cors(networkConfig.getCorsConfig()));

// Body parsing with size limits
app.use(bodyParser.json({
    limit: '10mb',
    verify: (req, res, buf) => {
        // Store raw body for signature verification if needed
        req.rawBody = buf;
    }
}));
app.use(bodyParser.urlencoded({ extended: true, limit: '10mb' }));

// Input validation middleware
app.use(validateInput());

// Static file serving
app.use('/public', express.static(path.join(__dirname, 'public')));
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

// Configure multer for file uploads with enhanced security
const fileUploadConfig = securityConfig.get('fileUpload');
const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        if (file.fieldname === 'studentPhoto') {
            cb(null, 'uploads/student-photos/');
        } else {
            cb(null, 'uploads/csv/');
        }
    },
    filename: function (req, file, cb) {
        // Generate secure filename with timestamp and random suffix
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        const sanitizedName = file.originalname.replace(/[^a-zA-Z0-9.-]/g, '_');
        cb(null, file.fieldname + '-' + uniqueSuffix + '-' + sanitizedName);
    }
});

const upload = multer({
    storage: storage,
    limits: {
        fileSize: fileUploadConfig.maxSize,
        files: fileUploadConfig.maxFiles || 5
    },
    fileFilter: function (req, file, cb) {
        // Enhanced file type validation
        const allowedTypes = file.fieldname === 'studentPhoto'
            ? ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
            : fileUploadConfig.allowedTypes;

        if (allowedTypes.includes(file.mimetype)) {
            cb(null, true);
        } else {
            cb(new Error(`File type ${file.mimetype} not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
        }
    }
});

// Make upload middleware available globally with security wrapper
app.locals.upload = upload;
app.locals.secureUpload = (fieldName, options = {}) => {
    return [
        upload.single(fieldName),
        secureFileUpload(options)
    ];
};

// Request logging middleware
app.use((req, res, next) => {
    console.log(`${moment().format('YYYY-MM-DD HH:mm:ss')} - ${req.method} ${req.path}`);
    next();
});

// Error handling middleware for multer
app.use((error, req, res, next) => {
    if (error instanceof multer.MulterError) {
        if (error.code === 'LIMIT_FILE_SIZE') {
            return res.status(400).json({
                success: false,
                message: 'File too large. Maximum size is 5MB.'
            });
        }
    }
    next(error);
});

// Load user middleware for templates
app.use(require('./middleware/auth').loadUser);

// Public routes (no authentication required)
app.use('/auth', require('./routes/auth'));
app.use('/health', (req, res, next) => next()); // Health check is public

// Apply brute force protection to login endpoints
app.use('/auth/login', bruteForceProtection);

// Protected routes (require authentication)
app.use('/', requireAuth, require('./routes/dashboard'));

// Admin routes (require admin role)
app.use('/users', requireAuth, requireRole(['admin', 'principal']), require('./routes/users'));
app.use('/backup', requireAuth, requireRole(['admin']), require('./routes/backup').router);
app.use('/network-config', requireAuth, requireRole(['admin']), require('./routes/network-config'));

// Teacher/Staff routes (require appropriate permissions)
app.use('/students', requireAuth, requirePermission('students.view'), require('./routes/students'));
app.use('/subjects', requireAuth, requirePermission('sessions.view'), require('./routes/subjects'));
app.use('/sessions', requireAuth, requirePermission('sessions.view'), require('./routes/sessions'));
app.use('/attendance', requireAuth, requirePermission('attendance.view'), require('./routes/attendance'));
app.use('/schedule', requireAuth, requirePermission('sessions.view'), require('./routes/schedule'));
app.use('/reports', requireAuth, requirePermission('reports.view'), require('./routes/reports'));

// Monitoring routes (require appropriate permissions)
app.use('/monitoring', requireAuth, requirePermission('system.logs'), require('./routes/monitoring').router);
app.use('/offline', requireAuth, require('./routes/offline').router);

// Security management routes
app.use('/security', requireAuth, require('./routes/security'));

// API routes with enhanced security
app.use('/api', requireAuth, validateInput(), (req, res, next) => {
    // Additional API-specific security checks can be added here
    next();
});

// Root route redirects to login or dashboard
app.get('/', (req, res) => {
    if (req.session.userId) {
        res.redirect('/dashboard');
    } else {
        res.redirect('/login');
    }
});

// Health check endpoint
app.get('/health', async (req, res) => {
    try {
        // Check database connection
        const db = dbConnection.getDatabase();
        if (!db) {
            throw new Error('Database not connected');
        }
        
        res.json({
            status: 'healthy',
            database: 'connected',
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
        });
    } catch (error) {
        res.status(500).json({
            status: 'unhealthy',
            error: error.message,
            timestamp: moment().format('YYYY-MM-DD HH:mm:ss')
        });
    }
});

// 404 handler
app.use('*', (req, res) => {
    res.status(404).json({
        success: false,
        message: 'Route not found',
        path: req.originalUrl
    });
});

// Global error handler
app.use((err, req, res, next) => {
    console.error('Error:', err.stack);
    res.status(500).json({
        success: false,
        message: 'Internal server error',
        error: process.env.NODE_ENV === 'development' ? err.message : 'Something went wrong'
    });
});

// Initialize security services
async function initializeSecurity() {
    try {
        console.log('Initializing security services...');

        // Initialize permission service (sets up default permissions and roles)
        await permissionService.initializeDefaultPermissions();
        console.log('✓ Permission service initialized');

        // Check if encryption key rotation is needed
        if (encryptionService.isKeyRotationNeeded()) {
            console.log('⚠ Encryption key rotation recommended');
        }

        // Log security initialization
        await auditLogger.log(
            null,
            'security_initialization',
            'system',
            'security',
            null,
            {
                timestamp: new Date().toISOString(),
                services: ['permissions', 'encryption', 'audit']
            },
            null,
            null,
            null,
            'info',
            'system'
        );

        console.log('✓ Security services initialized successfully');

    } catch (error) {
        console.error('Failed to initialize security services:', error);
        throw error;
    }
}

// Initialize database and start server
async function startServer() {
    try {
        // Connect to database
        await dbConnection.connect();
        console.log('✓ Database connected successfully');

        // Initialize database tables
        await dbConnection.initializeTables();
        console.log('✓ Database tables initialized');

        // Initialize security services
        await initializeSecurity();

        // Start server with network configuration
        const server = app.listen(PORT, serverConfig.host, () => {
            const setupInstructions = networkConfig.getNetworkSetupInstructions();
            console.log(`\n=== QR-SAMS Secure Server Started ===`);
            console.log(`Server running on: ${serverConfig.host}:${PORT}`);
            console.log(`Local Access URL: ${setupInstructions.accessURL}`);
            console.log(`Network IP: ${setupInstructions.serverIP}`);
            if (setupInstructions.httpsURL) {
                console.log(`HTTPS URL: ${setupInstructions.httpsURL}`);
            }
            console.log(`Health check: ${setupInstructions.accessURL}/health`);
            console.log(`Network setup: ${setupInstructions.accessURL}/network-config`);
            console.log(`Security Features: ✓ Enabled`);
            console.log(`- Authentication & 2FA`);
            console.log(`- Role-based Access Control`);
            console.log(`- Data Encryption`);
            console.log(`- Audit Logging`);
            console.log(`- Rate Limiting`);
            console.log(`- Security Monitoring`);
            console.log(`==========================================\n`);
        });

        // Log server startup
        await auditLogger.log(
            null,
            'server_started',
            'system',
            'server',
            null,
            {
                host: serverConfig.host,
                port: PORT,
                timestamp: new Date().toISOString()
            },
            null,
            null,
            null,
            'info',
            'system'
        );

    } catch (error) {
        console.error('Failed to start server:', error.message);
        process.exit(1);
    }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
    console.log('\nShutting down server...');
    await dbConnection.close();
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\nShutting down server...');
    await dbConnection.close();
    process.exit(0);
});

// Start the server
startServer();

module.exports = app;
