{"name": "qrsams", "version": "1.0.0", "description": "QR-Code Based Student Attendance and Monitoring System", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "init-db": "node database/init-db.js", "check-db": "node database/check-db.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["qr-code", "attendance", "student", "monitoring", "education", "express", "sqlite"], "author": "", "license": "ISC", "dependencies": {"@tensorflow/tfjs": "^4.22.0", "archiver": "^7.0.1", "axios": "^1.11.0", "bcrypt": "^5.1.1", "better-sqlite3": "^12.2.0", "body-parser": "^1.20.2", "chart.js": "^4.5.0", "chartjs-adapter-date-fns": "^3.0.0", "connect-sqlite3": "^0.9.16", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csurf": "^1.11.0", "csv-parser": "^3.2.0", "date-fns": "^4.1.0", "ejs": "^3.1.10", "exceljs": "^4.4.0", "express": "^4.18.2", "express-brute": "^1.0.1", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^8.0.1", "express-session": "^1.18.2", "express-slow-down": "^2.1.0", "express-validator": "^7.2.1", "extract-zip": "^2.0.1", "helmet": "^8.1.0", "hpp": "^0.2.3", "jsqr": "^1.4.0", "ml-matrix": "^6.12.1", "moment": "^2.29.4", "multer": "^1.4.5-lts.1", "nodemailer": "^7.0.5", "path": "^0.12.7", "puppeteer": "^24.15.0", "qrcode": "^1.5.3", "qrcode-terminal": "^0.12.0", "simple-statistics": "^7.8.8", "speakeasy": "^2.0.0", "sqlite3": "^5.1.6", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.1"}}