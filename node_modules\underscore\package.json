{"name": "underscore", "description": "JavaScript's functional programming helper library.", "homepage": "http://underscorejs.org", "keywords": ["util", "functional", "server", "client", "browser"], "author": "<PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "git://github.com/jashkenas/underscore.git"}, "main": "underscore.js", "version": "1.8.3", "devDependencies": {"docco": "*", "eslint": "0.6.x", "karma": "~0.12.31", "karma-qunit": "~0.1.4", "qunit-cli": "~0.2.0", "uglify-js": "2.4.x"}, "scripts": {"test": "npm run test-node && npm run lint", "lint": "eslint underscore.js test/*.js", "test-node": "qunit-cli test/*.js", "test-browser": "npm i karma-phantomjs-launcher && ./node_modules/karma/bin/karma start", "build": "uglifyjs underscore.js -c \"evaluate=false\" --comments \"/    .*/\" -m --source-map underscore-min.map -o underscore-min.js", "doc": "docco underscore.js"}, "license": "MIT", "files": ["underscore.js", "underscore-min.js", "underscore-min.map", "LICENSE"]}