{"name": "fuzz", "version": "1.0.0", "description": "Fuzzer for xss-filters", "main": "app.js", "bin": {"fuzz": "fuzzer.js"}, "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "repository": {"type": "git", "url": "https://github.com/yahoo/xss-filters"}, "keywords": ["xss-filters", "fuzz", "yahoo"], "author": "stuart<PERSON><EMAIL>", "licenses": [{"type": "BSD", "url": "https://github.com/yahoo/xss-filters/blob/master/LICENSE"}], "bugs": {"url": "https://github.com/yahoo/xss-filters/issues"}, "homepage": "https://github.com/yahoo/xss-filters", "dependencies": {"async": "^0.9.2", "express": "^4.12.4", "redis": "^0.12.1", "zombie": "^4.0.10"}}