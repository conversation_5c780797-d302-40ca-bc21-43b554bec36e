const dbConnection = require('../database/connection');
const auditLogger = require('./audit-logger');

/**
 * Permission Service
 * Handles role-based access control and permissions
 */
class PermissionService {
    constructor() {
        this.db = null;
        this.permissionCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5 minutes
    }

    // Initialize database connection
    initializeDatabase() {
        if (!this.db) {
            this.db = dbConnection.getDatabase();
        }
        return this.db;
    }

    /**
     * Initialize default permissions and role assignments
     */
    async initializeDefaultPermissions() {
        try {
            this.initializeDatabase();
            const defaultPermissions = [
                // User Management
                { name: 'users.view', description: 'View users', category: 'user_management', resource_type: 'user', action_type: 'read' },
                { name: 'users.create', description: 'Create users', category: 'user_management', resource_type: 'user', action_type: 'create' },
                { name: 'users.edit', description: 'Edit users', category: 'user_management', resource_type: 'user', action_type: 'update' },
                { name: 'users.delete', description: 'Delete users', category: 'user_management', resource_type: 'user', action_type: 'delete' },
                { name: 'users.manage_roles', description: 'Manage user roles', category: 'user_management', resource_type: 'user', action_type: 'manage' },

                // Student Management
                { name: 'students.view', description: 'View students', category: 'student_management', resource_type: 'student', action_type: 'read' },
                { name: 'students.create', description: 'Create students', category: 'student_management', resource_type: 'student', action_type: 'create' },
                { name: 'students.edit', description: 'Edit students', category: 'student_management', resource_type: 'student', action_type: 'update' },
                { name: 'students.delete', description: 'Delete students', category: 'student_management', resource_type: 'student', action_type: 'delete' },
                { name: 'students.import', description: 'Import students', category: 'student_management', resource_type: 'student', action_type: 'import' },
                { name: 'students.export', description: 'Export students', category: 'student_management', resource_type: 'student', action_type: 'export' },

                // Attendance Management
                { name: 'attendance.view', description: 'View attendance', category: 'attendance', resource_type: 'attendance', action_type: 'read' },
                { name: 'attendance.record', description: 'Record attendance', category: 'attendance', resource_type: 'attendance', action_type: 'create' },
                { name: 'attendance.edit', description: 'Edit attendance', category: 'attendance', resource_type: 'attendance', action_type: 'update' },
                { name: 'attendance.delete', description: 'Delete attendance', category: 'attendance', resource_type: 'attendance', action_type: 'delete' },

                // Class Sessions
                { name: 'sessions.view', description: 'View class sessions', category: 'sessions', resource_type: 'session', action_type: 'read' },
                { name: 'sessions.create', description: 'Create class sessions', category: 'sessions', resource_type: 'session', action_type: 'create' },
                { name: 'sessions.edit', description: 'Edit class sessions', category: 'sessions', resource_type: 'session', action_type: 'update' },
                { name: 'sessions.delete', description: 'Delete class sessions', category: 'sessions', resource_type: 'session', action_type: 'delete' },

                // Reports
                { name: 'reports.view', description: 'View reports', category: 'reports', resource_type: 'report', action_type: 'read' },
                { name: 'reports.generate', description: 'Generate reports', category: 'reports', resource_type: 'report', action_type: 'create' },
                { name: 'reports.export', description: 'Export reports', category: 'reports', resource_type: 'report', action_type: 'export' },
                { name: 'reports.advanced', description: 'Access advanced reports', category: 'reports', resource_type: 'report', action_type: 'advanced' },

                // System Administration
                { name: 'system.settings', description: 'Manage system settings', category: 'system', resource_type: 'system', action_type: 'manage' },
                { name: 'system.backup', description: 'Manage backups', category: 'system', resource_type: 'system', action_type: 'backup' },
                { name: 'system.logs', description: 'View system logs', category: 'system', resource_type: 'system', action_type: 'logs' },
                { name: 'system.security', description: 'Manage security settings', category: 'system', resource_type: 'system', action_type: 'security' },

                // SMS and Communication
                { name: 'sms.send', description: 'Send SMS messages', category: 'communication', resource_type: 'sms', action_type: 'send' },
                { name: 'sms.view_logs', description: 'View SMS logs', category: 'communication', resource_type: 'sms', action_type: 'read' },
                { name: 'sms.manage_templates', description: 'Manage SMS templates', category: 'communication', resource_type: 'sms', action_type: 'manage' },

                // Data Privacy
                { name: 'privacy.view_consents', description: 'View privacy consents', category: 'privacy', resource_type: 'privacy', action_type: 'read' },
                { name: 'privacy.manage_consents', description: 'Manage privacy consents', category: 'privacy', resource_type: 'privacy', action_type: 'manage' },
                { name: 'privacy.export_data', description: 'Export user data', category: 'privacy', resource_type: 'privacy', action_type: 'export' },
                { name: 'privacy.delete_data', description: 'Delete user data', category: 'privacy', resource_type: 'privacy', action_type: 'delete' },

                // Security and Audit
                { name: 'security.view_events', description: 'View security events', category: 'security', resource_type: 'security', action_type: 'read' },
                { name: 'security.manage_alerts', description: 'Manage security alerts', category: 'security', resource_type: 'security', action_type: 'manage' },
                { name: 'audit.view_logs', description: 'View audit logs', category: 'audit', resource_type: 'audit', action_type: 'read' },
                { name: 'audit.export_logs', description: 'Export audit logs', category: 'audit', resource_type: 'audit', action_type: 'export' }
            ];

            // Insert permissions if they don't exist
            for (const permission of defaultPermissions) {
                await this.createPermissionIfNotExists(permission);
            }

            // Setup default role permissions
            await this.setupDefaultRolePermissions();

        } catch (error) {
            console.error('Error initializing permissions:', error);
        }
    }

    /**
     * Create permission if it doesn't exist
     * @param {Object} permission - Permission object
     */
    async createPermissionIfNotExists(permission) {
        try {
            this.initializeDatabase();
            const existing = await new Promise((resolve, reject) => {
                this.db.get(
                    'SELECT permission_id FROM permissions WHERE permission_name = ?',
                    [permission.name],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });

            if (!existing) {
                await new Promise((resolve, reject) => {
                    this.db.run(
                        `INSERT INTO permissions 
                         (permission_name, description, category, resource_type, action_type)
                         VALUES (?, ?, ?, ?, ?)`,
                        [permission.name, permission.description, permission.category, 
                         permission.resource_type, permission.action_type],
                        (err) => {
                            if (err) reject(err);
                            else resolve();
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Error creating permission:', error);
        }
    }

    /**
     * Setup default role permissions
     */
    async setupDefaultRolePermissions() {
        const rolePermissions = {
            'admin': [
                // Full access to everything
                'users.view', 'users.create', 'users.edit', 'users.delete', 'users.manage_roles',
                'students.view', 'students.create', 'students.edit', 'students.delete', 'students.import', 'students.export',
                'attendance.view', 'attendance.record', 'attendance.edit', 'attendance.delete',
                'sessions.view', 'sessions.create', 'sessions.edit', 'sessions.delete',
                'reports.view', 'reports.generate', 'reports.export', 'reports.advanced',
                'system.settings', 'system.backup', 'system.logs', 'system.security',
                'sms.send', 'sms.view_logs', 'sms.manage_templates',
                'privacy.view_consents', 'privacy.manage_consents', 'privacy.export_data', 'privacy.delete_data',
                'security.view_events', 'security.manage_alerts', 'audit.view_logs', 'audit.export_logs'
            ],
            'principal': [
                // Administrative access with some restrictions
                'users.view', 'users.create', 'users.edit',
                'students.view', 'students.create', 'students.edit', 'students.import', 'students.export',
                'attendance.view', 'attendance.record', 'attendance.edit',
                'sessions.view', 'sessions.create', 'sessions.edit',
                'reports.view', 'reports.generate', 'reports.export', 'reports.advanced',
                'system.backup', 'system.logs',
                'sms.send', 'sms.view_logs', 'sms.manage_templates',
                'privacy.view_consents', 'privacy.manage_consents',
                'security.view_events', 'audit.view_logs'
            ],
            'teacher': [
                // Teaching-focused permissions
                'students.view', 'students.edit',
                'attendance.view', 'attendance.record', 'attendance.edit',
                'sessions.view', 'sessions.create', 'sessions.edit',
                'reports.view', 'reports.generate',
                'sms.send', 'sms.view_logs'
            ],
            'staff': [
                // Basic staff permissions
                'students.view',
                'attendance.view', 'attendance.record',
                'sessions.view',
                'reports.view'
            ]
        };

        for (const [role, permissions] of Object.entries(rolePermissions)) {
            for (const permissionName of permissions) {
                await this.assignRolePermission(role, permissionName);
            }
        }
    }

    /**
     * Assign permission to role
     * @param {string} role - Role name
     * @param {string} permissionName - Permission name
     */
    async assignRolePermission(role, permissionName) {
        try {
            const permission = await this.getPermissionByName(permissionName);
            if (!permission) return;

            const existing = await new Promise((resolve, reject) => {
                this.db.get(
                    'SELECT role_permission_id FROM role_permissions WHERE role = ? AND permission_id = ?',
                    [role, permission.permission_id],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });

            if (!existing) {
                await new Promise((resolve, reject) => {
                    this.db.run(
                        'INSERT INTO role_permissions (role, permission_id, granted) VALUES (?, ?, 1)',
                        [role, permission.permission_id],
                        (err) => {
                            if (err) reject(err);
                            else resolve();
                        }
                    );
                });
            }
        } catch (error) {
            console.error('Error assigning role permission:', error);
        }
    }

    /**
     * Check if user has permission
     * @param {number} userId - User ID
     * @param {string} permissionName - Permission name
     * @param {string} resourceId - Resource ID (optional)
     * @returns {Promise<boolean>} True if user has permission
     */
    async hasPermission(userId, permissionName, resourceId = null) {
        try {
            const cacheKey = `${userId}:${permissionName}:${resourceId || 'global'}`;
            
            // Check cache first
            if (this.permissionCache.has(cacheKey)) {
                const cached = this.permissionCache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    return cached.hasPermission;
                }
            }

            const user = await this.getUserById(userId);
            if (!user) return false;

            // Check user-specific permissions first
            const userPermission = await this.getUserPermission(userId, permissionName);
            if (userPermission !== null) {
                this.cachePermission(cacheKey, userPermission);
                return userPermission;
            }

            // Check role-based permissions
            const rolePermission = await this.getRolePermission(user.role, permissionName);
            
            this.cachePermission(cacheKey, rolePermission);
            return rolePermission;

        } catch (error) {
            console.error('Permission check error:', error);
            return false;
        }
    }

    /**
     * Get user permission override
     * @param {number} userId - User ID
     * @param {string} permissionName - Permission name
     * @returns {Promise<boolean|null>} Permission status or null if not set
     */
    async getUserPermission(userId, permissionName) {
        try {
            const result = await new Promise((resolve, reject) => {
                this.db.get(
                    `SELECT up.granted 
                     FROM user_permissions up
                     JOIN permissions p ON up.permission_id = p.permission_id
                     WHERE up.user_id = ? AND p.permission_name = ? 
                     AND (up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP)`,
                    [userId, permissionName],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });

            return result ? Boolean(result.granted) : null;
        } catch (error) {
            console.error('Get user permission error:', error);
            return null;
        }
    }

    /**
     * Get role permission
     * @param {string} role - Role name
     * @param {string} permissionName - Permission name
     * @returns {Promise<boolean>} Permission status
     */
    async getRolePermission(role, permissionName) {
        try {
            const result = await new Promise((resolve, reject) => {
                this.db.get(
                    `SELECT rp.granted 
                     FROM role_permissions rp
                     JOIN permissions p ON rp.permission_id = p.permission_id
                     WHERE rp.role = ? AND p.permission_name = ?`,
                    [role, permissionName],
                    (err, row) => {
                        if (err) reject(err);
                        else resolve(row);
                    }
                );
            });

            return result ? Boolean(result.granted) : false;
        } catch (error) {
            console.error('Get role permission error:', error);
            return false;
        }
    }

    /**
     * Grant permission to user
     * @param {number} userId - User ID
     * @param {string} permissionName - Permission name
     * @param {number} grantedBy - User ID who granted the permission
     * @param {Date} expiresAt - Expiration date (optional)
     * @param {string} reason - Reason for granting
     * @returns {Promise<boolean>} Success status
     */
    async grantUserPermission(userId, permissionName, grantedBy, expiresAt = null, reason = null) {
        try {
            const permission = await this.getPermissionByName(permissionName);
            if (!permission) return false;

            await new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT OR REPLACE INTO user_permissions 
                     (user_id, permission_id, granted, granted_by, granted_at, expires_at, reason)
                     VALUES (?, ?, 1, ?, CURRENT_TIMESTAMP, ?, ?)`,
                    [userId, permission.permission_id, grantedBy, 
                     expiresAt ? expiresAt.toISOString() : null, reason],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            // Clear cache
            this.clearUserPermissionCache(userId);

            // Log the action
            await auditLogger.log(
                grantedBy, 
                'permission_granted', 
                'user_permission', 
                userId,
                null,
                { permissionName, reason, expiresAt }
            );

            return true;
        } catch (error) {
            console.error('Grant user permission error:', error);
            return false;
        }
    }

    /**
     * Revoke permission from user
     * @param {number} userId - User ID
     * @param {string} permissionName - Permission name
     * @param {number} revokedBy - User ID who revoked the permission
     * @param {string} reason - Reason for revoking
     * @returns {Promise<boolean>} Success status
     */
    async revokeUserPermission(userId, permissionName, revokedBy, reason = null) {
        try {
            const permission = await this.getPermissionByName(permissionName);
            if (!permission) return false;

            await new Promise((resolve, reject) => {
                this.db.run(
                    `INSERT OR REPLACE INTO user_permissions 
                     (user_id, permission_id, granted, granted_by, granted_at, reason)
                     VALUES (?, ?, 0, ?, CURRENT_TIMESTAMP, ?)`,
                    [userId, permission.permission_id, revokedBy, reason],
                    (err) => {
                        if (err) reject(err);
                        else resolve();
                    }
                );
            });

            // Clear cache
            this.clearUserPermissionCache(userId);

            // Log the action
            await auditLogger.log(
                revokedBy, 
                'permission_revoked', 
                'user_permission', 
                userId,
                null,
                { permissionName, reason }
            );

            return true;
        } catch (error) {
            console.error('Revoke user permission error:', error);
            return false;
        }
    }

    /**
     * Get all permissions for user
     * @param {number} userId - User ID
     * @returns {Promise<Array>} User permissions
     */
    async getUserPermissions(userId) {
        try {
            const user = await this.getUserById(userId);
            if (!user) return [];

            // Get role permissions
            const rolePermissions = await new Promise((resolve, reject) => {
                this.db.all(
                    `SELECT p.permission_name, p.description, p.category, 'role' as source
                     FROM role_permissions rp
                     JOIN permissions p ON rp.permission_id = p.permission_id
                     WHERE rp.role = ? AND rp.granted = 1`,
                    [user.role],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows);
                    }
                );
            });

            // Get user-specific permissions
            const userPermissions = await new Promise((resolve, reject) => {
                this.db.all(
                    `SELECT p.permission_name, p.description, p.category, 
                            CASE WHEN up.granted = 1 THEN 'granted' ELSE 'revoked' END as source,
                            up.expires_at, up.reason
                     FROM user_permissions up
                     JOIN permissions p ON up.permission_id = p.permission_id
                     WHERE up.user_id = ? 
                     AND (up.expires_at IS NULL OR up.expires_at > CURRENT_TIMESTAMP)`,
                    [userId],
                    (err, rows) => {
                        if (err) reject(err);
                        else resolve(rows);
                    }
                );
            });

            // Merge permissions, user-specific overrides role permissions
            const permissionMap = new Map();
            
            // Add role permissions first
            rolePermissions.forEach(perm => {
                permissionMap.set(perm.permission_name, perm);
            });

            // Override with user-specific permissions
            userPermissions.forEach(perm => {
                if (perm.source === 'granted') {
                    permissionMap.set(perm.permission_name, perm);
                } else if (perm.source === 'revoked') {
                    permissionMap.delete(perm.permission_name);
                }
            });

            return Array.from(permissionMap.values());

        } catch (error) {
            console.error('Get user permissions error:', error);
            return [];
        }
    }

    /**
     * Helper methods
     */
    async getPermissionByName(permissionName) {
        this.initializeDatabase();
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM permissions WHERE permission_name = ?',
                [permissionName],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    async getUserById(userId) {
        return new Promise((resolve, reject) => {
            this.db.get(
                'SELECT * FROM users WHERE user_id = ?',
                [userId],
                (err, row) => {
                    if (err) reject(err);
                    else resolve(row);
                }
            );
        });
    }

    cachePermission(cacheKey, hasPermission) {
        this.permissionCache.set(cacheKey, {
            hasPermission,
            timestamp: Date.now()
        });
    }

    clearUserPermissionCache(userId) {
        for (const [key] of this.permissionCache) {
            if (key.startsWith(`${userId}:`)) {
                this.permissionCache.delete(key);
            }
        }
    }

    clearPermissionCache() {
        this.permissionCache.clear();
    }
}

module.exports = new PermissionService();
